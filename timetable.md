# Student Timetable Mobile API Documentation

## Current Backend Implementation Analysis

### Existing Timetable Endpoints

Based on the backend analysis, the following timetable-related endpoints exist:

#### 1. Timetable Management Endpoints
```php
// From routes/api.php and TimeTableController.php
Route::controller(TimeTableController::class)->group(function () {
    Route::get('/timetables', 'index');
    Route::post('/timetables', 'store');
    Route::get('/timetables/{id}', 'show');
    Route::patch('/timetables/{id}', 'update');
    Route::delete('/timetables/{id}', 'destroy');
});
```

#### 2. Current Implementation Details

**TimeTableController Structure:**
- Located in `app/Http/Controllers/API/TimeTable/TimeTableController.php`
- Manages timetable profiles and activities
- Filters by faculty, programme, session, and semester

**Data Relationships:**
```php
// Key models involved:
- TimeTable
- TimeTableProfile  
- TimeTableActivity
- Session
- Semester
- Faculty
- Programme
- Course
- LectureVenue
- LectureDay
- LecturePeriod
```

**Current Filtering Logic:**
```php
// From TimeTableController validation
'time_table_profile' => 'required|string',
'session' => 'required|string', 
'faculty' => 'required|string',
'programme' => 'nullable|string'
```

## Proposed Mobile API Implementation

### New Mobile Endpoint Structure

#### 1. Get Student Timetable
```
GET /v1/mobile-app/student/timetable
```

**Query Parameters:**
- `session_id` (optional): Filter by specific session (defaults to current)
- `semester_id` (optional): Filter by specific semester (defaults to current)
- `week_start` (optional): Start date for weekly view (YYYY-MM-DD)
- `view_type` (optional): 'weekly' | 'daily' | 'full' (default: 'weekly')

**Response:**
```json
{
  "success": true,
  "data": {
    "student_info": {
      "student_number": "20/56EB094",
      "fullname": "John Doe",
      "programme": "B.Sc Computer Science",
      "level": "400 Level",
      "faculty": "Science"
    },
    "session_info": {
      "id": 1,
      "session_name": "2023/2024",
      "current_semester": {
        "id": 1,
        "title": "First Semester",
        "accronym": "FIRST"
      }
    },
    "timetable": {
      "week_start": "2024-01-15",
      "week_end": "2024-01-21",
      "days": [
        {
          "date": "2024-01-15",
          "day_name": "Monday",
          "activities": [
            {
              "id": 1,
              "course": {
                "code": "CSC401",
                "title": "Software Engineering",
                "credit_unit": 3
              },
              "activity_type": "Lecture",
              "start_time": "08:00",
              "end_time": "10:00",
              "duration_minutes": 120,
              "venue": {
                "code": "LT1",
                "name": "Lecture Theatre 1",
                "capacity": 200,
                "building": "Computer Science Building"
              },
              "lecturer": {
                "name": "Dr. John Smith",
                "title": "Senior Lecturer"
              },
              "period": "1st Period",
              "is_current": false,
              "is_upcoming": true
            }
          ]
        }
      ],
      "summary": {
        "total_activities": 15,
        "total_courses": 8,
        "total_credit_units": 24,
        "activities_by_type": {
          "Lecture": 10,
          "Practical": 3,
          "Tutorial": 2
        }
      }
    }
  }
}
```

#### 2. Get Today's Schedule
```
GET /v1/mobile-app/student/timetable/today
```

**Response:**
```json
{
  "success": true,
  "data": {
    "date": "2024-01-15",
    "day_name": "Monday",
    "current_time": "09:30",
    "current_activity": {
      "course": {
        "code": "CSC401",
        "title": "Software Engineering"
      },
      "activity_type": "Lecture",
      "start_time": "08:00",
      "end_time": "10:00",
      "venue": "LT1",
      "lecturer": "Dr. John Smith",
      "time_remaining_minutes": 30
    },
    "next_activity": {
      "course": {
        "code": "MTH402",
        "title": "Numerical Analysis"
      },
      "activity_type": "Lecture", 
      "start_time": "10:00",
      "end_time": "12:00",
      "venue": "LT2",
      "lecturer": "Prof. Jane Doe",
      "starts_in_minutes": 30
    },
    "all_activities": [
      // Full day schedule
    ],
    "summary": {
      "total_activities": 4,
      "completed": 1,
      "current": 1,
      "upcoming": 2,
      "free_periods": [
        {
          "start_time": "12:00",
          "end_time": "14:00",
          "duration_minutes": 120
        }
      ]
    }
  }
}
```

#### 3. Get Course Schedule
```
GET /v1/mobile-app/student/timetable/course/{course_id}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "course": {
      "id": 1,
      "code": "CSC401",
      "title": "Software Engineering",
      "credit_unit": 3,
      "lecturer": "Dr. John Smith"
    },
    "schedule": [
      {
        "day": "Monday",
        "activities": [
          {
            "activity_type": "Lecture",
            "start_time": "08:00",
            "end_time": "10:00",
            "venue": "LT1",
            "recurring": "Weekly"
          }
        ]
      }
    ],
    "next_class": {
      "date": "2024-01-15",
      "start_time": "08:00",
      "venue": "LT1",
      "activity_type": "Lecture"
    }
  }
}
```

### Implementation Requirements

#### 1. New Controller
Create `MobileApp\v1\Student\Timetable\TimetableController.php`:

```php
<?php

namespace App\Http\Controllers\MobileApp\v1\Student\Timetable;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Carbon\Carbon;

class TimetableController extends Controller
{
    public function index(Request $request)
    {
        $student = $request->user()->userable;
        
        $sessionId = $request->query('session_id') ?? $this->getCurrentSessionId($student);
        $semesterId = $request->query('semester_id') ?? $this->getCurrentSemesterId($student);
        $weekStart = $request->query('week_start') ?? Carbon::now()->startOfWeek()->format('Y-m-d');
        $viewType = $request->query('view_type', 'weekly');
        
        $timetable = $this->getStudentTimetable($student, $sessionId, $semesterId, $weekStart, $viewType);
        
        return response()->json([
            'success' => true,
            'data' => $timetable
        ]);
    }
    
    public function today(Request $request)
    {
        $student = $request->user()->userable;
        $today = Carbon::now();
        
        $todaySchedule = $this->getTodaySchedule($student, $today);
        
        return response()->json([
            'success' => true,
            'data' => $todaySchedule
        ]);
    }
    
    public function courseSchedule(Request $request, $courseId)
    {
        $student = $request->user()->userable;
        
        // Verify student is registered for this course
        $courseSchedule = $this->getCourseSchedule($student, $courseId);
        
        return response()->json([
            'success' => true,
            'data' => $courseSchedule
        ]);
    }
    
    private function getStudentTimetable($student, $sessionId, $semesterId, $weekStart, $viewType)
    {
        // Get student's registered courses for the session/semester
        $registeredCourses = $student->courseRegistrations()
            ->where('session_id', $sessionId)
            ->where('semester_id', $semesterId)
            ->with(['course', 'session', 'semester'])
            ->get();
        
        // Get timetable activities for these courses
        $activities = $this->getTimetableActivities($registeredCourses, $weekStart, $viewType);
        
        return $this->formatTimetableResponse($student, $activities, $weekStart, $viewType);
    }
    
    private function getTimetableActivities($registeredCourses, $weekStart, $viewType)
    {
        $courseIds = $registeredCourses->pluck('course_id');
        
        // Query timetable activities
        return TimeTableActivity::whereHas('activity.course', function($query) use ($courseIds) {
            $query->whereIn('id', $courseIds);
        })
        ->with([
            'activity.course',
            'activity.lecturer',
            'venue',
            'lectureDay',
            'lecturePeriod'
        ])
        ->get();
    }
    
    private function formatTimetableResponse($student, $activities, $weekStart, $viewType)
    {
        // Format the response according to the mobile app requirements
        // Group activities by day, sort by time, add current/upcoming flags
    }
    
    private function getTodaySchedule($student, $today)
    {
        // Get today's activities with current/next activity logic
    }
    
    private function getCourseSchedule($student, $courseId)
    {
        // Get schedule for specific course
    }
}
```

#### 2. New Route File
Create `routes/mobileApp/v1/student/timetable/api.php`:

```php
<?php

use App\Http\Controllers\MobileApp\v1\Student\Timetable\TimetableController;
use Illuminate\Support\Facades\Route;

Route::middleware('auth:sanctum')->group(function () {
    Route::get('student/timetable', [TimetableController::class, 'index']);
    Route::get('student/timetable/today', [TimetableController::class, 'today']);
    Route::get('student/timetable/course/{course_id}', [TimetableController::class, 'courseSchedule']);
});
```

#### 3. Database Optimizations

**Required Relationships:**
```php
// Student -> CourseRegistrations -> Course -> TimetableActivities
$student->load([
    'courseRegistrations.course.timetableActivities.venue',
    'courseRegistrations.course.timetableActivities.lectureDay',
    'courseRegistrations.course.timetableActivities.lecturePeriod',
    'courseRegistrations.course.timetableActivities.activity.lecturer',
    'courseRegistrations.session',
    'courseRegistrations.semester'
]);
```

**Caching Strategy:**
- Cache timetable per student per week
- Cache key: `student_timetable_{student_id}_{session_id}_{week_start}`
- TTL: 24 hours (timetables change infrequently)

### Mobile App Integration

#### 1. Data Models
```dart
class TimetableActivity {
  final int id;
  final Course course;
  final String activityType;
  final String startTime;
  final String endTime;
  final int durationMinutes;
  final Venue venue;
  final Lecturer lecturer;
  final String period;
  final bool isCurrent;
  final bool isUpcoming;
}

class DaySchedule {
  final String date;
  final String dayName;
  final List<TimetableActivity> activities;
}

class WeeklyTimetable {
  final String weekStart;
  final String weekEnd;
  final List<DaySchedule> days;
  final TimetableSummary summary;
}
```

#### 2. Repository Implementation
```dart
class TimetableRepository {
  Future<WeeklyTimetable> getWeeklyTimetable({
    int? sessionId,
    int? semesterId,
    String? weekStart,
    String viewType = 'weekly',
  }) async {
    final response = await apiClient.get(
      '/student/timetable',
      queryParameters: {
        if (sessionId != null) 'session_id': sessionId,
        if (semesterId != null) 'semester_id': semesterId,
        if (weekStart != null) 'week_start': weekStart,
        'view_type': viewType,
      },
    );
    
    return WeeklyTimetable.fromJson(response.data['data']);
  }
  
  Future<DaySchedule> getTodaySchedule() async {
    final response = await apiClient.get('/student/timetable/today');
    return DaySchedule.fromJson(response.data['data']);
  }
}
```

### Key Features for Mobile App

1. **Real-time Updates**: Show current and next classes
2. **Notifications**: Remind students of upcoming classes
3. **Offline Support**: Cache timetable for offline viewing
4. **Calendar Integration**: Export to device calendar
5. **Venue Maps**: Integration with campus maps
6. **Class Reminders**: Push notifications before classes
7. **Free Period Detection**: Show available time slots
8. **Conflict Detection**: Highlight scheduling conflicts

### Security & Performance

1. **Authorization**: Students only see their registered courses
2. **Caching**: Aggressive caching with smart invalidation
3. **Rate Limiting**: Prevent excessive API calls
4. **Data Validation**: Validate all input parameters
5. **Error Handling**: Graceful handling of missing data

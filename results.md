# Student Results Mobile API Documentation

## Current Backend Implementation Analysis

### Existing Results Endpoints

Based on the backend analysis, the following results-related endpoints exist:

#### 1. Student Results Endpoints (StudentController.php)
```php
// Line 654-657 in routes/api.php
Route::patch('/students/student-results', 'getMyResults');
Route::get('/students/student-results/download/{student_id}/{session_id}/{programme_id}', 'downloadResults');
Route::get('/students/student-results-spreadsheet/{student_id}', 'getMyResultsSpreadSheet');
Route::get('/students/download-transcript/{student}', 'downloadStudentTranscript');
```

#### 2. Current Implementation Details

**getMyResults Method:**
- Located in `StudentController.php` around line 4547
- Returns JSON response with:
  - `results`: Student results grouped by session
  - `student`: Student profile information
  - `grade_point`: Final CGPA
  - `suspended_sessions`: Any suspended academic sessions
  - `graduation_year`: Expected graduation year

**Data Structure:**
```json
{
  "results": {
    "session_id": [
      {
        "course_code": "CSC101",
        "course_title": "Introduction to Computer Science",
        "credit_unit": 3,
        "ca_score": 30,
        "exam_score": 55,
        "total_score": 85,
        "grade": "A",
        "semester_title": "First Semester"
      }
    ]
  },
  "student": {
    "student_number": "20/56EB094",
    "fullname": "John Doe",
    "programme": "B.Sc Computer Science"
  },
  "grade_point": 3.45,
  "suspended_sessions": [],
  "graduation_year": "2024"
}
```

## Proposed Mobile API Implementation

### New Mobile Endpoint Structure

#### 1. Get Student Results
```
GET /v1/mobile-app/student/results
```

**Query Parameters:**
- `session_id` (optional): Filter by specific session
- `semester_id` (optional): Filter by specific semester
- `format` (optional): 'summary' | 'detailed' (default: 'detailed')

**Response:**
```json
{
  "success": true,
  "data": {
    "student_info": {
      "student_number": "20/56EB094",
      "fullname": "John Doe",
      "programme": "B.Sc Computer Science",
      "current_level": "400 Level",
      "current_session": "2023/2024",
      "current_semester": "First Semester"
    },
    "academic_summary": {
      "cgpa": 3.45,
      "total_credit_units": 120,
      "total_credit_passed": 115,
      "graduation_year": "2024"
    },
    "results_by_session": [
      {
        "session": {
          "id": 1,
          "session_name": "2023/2024"
        },
        "semesters": [
          {
            "semester": {
              "id": 1,
              "title": "First Semester",
              "accronym": "FIRST"
            },
            "gpa": 3.2,
            "total_units": 18,
            "courses": [
              {
                "course_code": "CSC401",
                "course_title": "Software Engineering",
                "credit_unit": 3,
                "ca_score": 28,
                "exam_score": 52,
                "total_score": 80,
                "grade": "B",
                "point": 4.0,
                "is_passed": true,
                "is_carryover": false
              }
            ]
          }
        ]
      }
    ]
  }
}
```

#### 2. Get Result Summary
```
GET /v1/mobile-app/student/results/summary
```

**Response:**
```json
{
  "success": true,
  "data": {
    "cgpa": 3.45,
    "current_gpa": 3.2,
    "total_credit_units": 120,
    "credit_units_passed": 115,
    "outstanding_courses": 2,
    "graduation_status": "On Track",
    "sessions_completed": 4,
    "current_session": "2023/2024",
    "expected_graduation": "2024"
  }
}
```

### Implementation Requirements

#### 1. New Controller
Create `MobileApp\v1\Student\Results\ResultController.php`:

```php
<?php

namespace App\Http\Controllers\MobileApp\v1\Student\Results;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ResultController extends Controller
{
    public function index(Request $request)
    {
        // Get authenticated student
        $student = $request->user()->userable;
        
        // Apply filters
        $sessionId = $request->query('session_id');
        $semesterId = $request->query('semester_id');
        $format = $request->query('format', 'detailed');
        
        // Fetch results with relationships
        $results = $this->getStudentResults($student, $sessionId, $semesterId);
        
        // Format response based on format parameter
        return response()->json([
            'success' => true,
            'data' => $format === 'summary' 
                ? $this->formatSummary($results)
                : $this->formatDetailed($results)
        ]);
    }
    
    public function summary(Request $request)
    {
        // Implementation for summary endpoint
    }
    
    private function getStudentResults($student, $sessionId = null, $semesterId = null)
    {
        // Reuse existing logic from StudentController::getMyResults
        // but optimize for mobile consumption
    }
    
    private function formatDetailed($results)
    {
        // Format for detailed mobile view
    }
    
    private function formatSummary($results)
    {
        // Format for summary view
    }
}
```

#### 2. New Route File
Create `routes/mobileApp/v1/student/results/api.php`:

```php
<?php

use App\Http\Controllers\MobileApp\v1\Student\Results\ResultController;
use Illuminate\Support\Facades\Route;

Route::middleware('auth:sanctum')->group(function () {
    Route::get('student/results', [ResultController::class, 'index']);
    Route::get('student/results/summary', [ResultController::class, 'summary']);
});
```

#### 3. Database Optimizations

**Recommended Eager Loading:**
```php
$student->load([
    'courseRegistrations.course',
    'courseRegistrations.session',
    'courseRegistrations.semester',
    'courseRegistrations.assessmentScores.assessment',
    'programme',
    'level'
]);
```

**Caching Strategy:**
- Cache results per student per session
- Cache key: `student_results_{student_id}_{session_id}`
- TTL: 1 hour (results don't change frequently)

### Mobile App Integration

#### 1. Data Models
```dart
class StudentResult {
  final String courseCode;
  final String courseTitle;
  final int creditUnit;
  final int caScore;
  final int examScore;
  final int totalScore;
  final String grade;
  final double point;
  final bool isPassed;
  final bool isCarryover;
}

class SemesterResult {
  final Semester semester;
  final double gpa;
  final int totalUnits;
  final List<StudentResult> courses;
}

class SessionResult {
  final Session session;
  final List<SemesterResult> semesters;
}

class StudentResultsResponse {
  final StudentInfo studentInfo;
  final AcademicSummary academicSummary;
  final List<SessionResult> resultsBySession;
}
```

#### 2. Repository Implementation
```dart
class ResultsRepository {
  Future<StudentResultsResponse> getStudentResults({
    int? sessionId,
    int? semesterId,
    String format = 'detailed',
  }) async {
    final response = await apiClient.get(
      '/student/results',
      queryParameters: {
        if (sessionId != null) 'session_id': sessionId,
        if (semesterId != null) 'semester_id': semesterId,
        'format': format,
      },
    );
    
    return StudentResultsResponse.fromJson(response.data['data']);
  }
}
```

### Security Considerations

1. **Authentication**: Ensure only authenticated students can access their results
2. **Authorization**: Students can only access their own results
3. **Data Validation**: Validate session_id and semester_id parameters
4. **Rate Limiting**: Implement rate limiting to prevent abuse
5. **Audit Logging**: Log all result access attempts

### Performance Optimizations

1. **Database Indexing**: Ensure proper indexes on frequently queried fields
2. **Query Optimization**: Use eager loading to reduce N+1 queries
3. **Caching**: Implement Redis caching for frequently accessed results
4. **Pagination**: For students with many sessions, implement pagination
5. **Compression**: Use gzip compression for large result sets

### Testing Strategy

1. **Unit Tests**: Test controller methods and data formatting
2. **Integration Tests**: Test API endpoints with real data
3. **Performance Tests**: Test with large datasets
4. **Security Tests**: Test authorization and data access controls

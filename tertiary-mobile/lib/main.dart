import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:tertiary_mobile/app.dart';
import 'package:tertiary_mobile/core/utils/logger.dart';
import 'package:tertiary_mobile/core/utils/shared_preferences.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  final packageInfo = await PackageInfo.fromPlatform();
  logger.i("App Name: ${packageInfo.appName}, Version: ${packageInfo.version}");

  // Initialize SharedPreferences
  await SharedPreferencesService.init();

  try {
    await dotenv.load(fileName: ".env");
    if (dotenv.env.isEmpty) {
      logger.e("The .env file is empty or not found.");
    }
  } catch (e) {
    logger.e("Failed to load .env file: $e");
  }

  runApp(
    ProviderScope(
      child: MyApp(),
    ),
  );
}

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:tertiary_mobile/core/constants/colors.dart';
import 'package:tertiary_mobile/features/authentication/data/models/user.dart';

class ProfileTables extends ConsumerWidget {
  final User user;

  const ProfileTables({
    super.key,
    required this.user,
  });

  TableRow _buildRow(
    String label,
    String value,
  ) {
    return TableRow(children: [
      // Label cell
      Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        child: Text(
          label,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
        ),
      ),
      // Value cell
      Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        child: Text(
          value,
          style: TextStyle(
            fontSize: 14,
          ),
        ),
      ),
    ]);
  }

  Widget _buildSectionHeader(String title, Color backgroundColor) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(8), topRight: Radius.circular(8)),
        color: backgroundColor,
      ),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      child: Text(
        title,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = ref.watch(appColorsProvider);

    final borderSide = BorderSide(
      color: Colors.grey.shade200,
      width: 1,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Bio Data Section
        _buildSectionHeader('Bio Data', colors.primary),
        Table(
          columnWidths: const {
            0: FlexColumnWidth(0.4),
            1: FlexColumnWidth(0.6),
          },
          border: TableBorder(
            horizontalInside: borderSide,
            verticalInside: borderSide,
          ),
          children: [
            _buildRow(
              'Full Name',
              user.fullname!,
            ),
            _buildRow(
              'Date of Birth',
              user.dateOfBirth ?? 'N/A',
            ),
            _buildRow(
              'Nationality',
              user.nationality!,
            ),
            _buildRow(
              'State of Origin',
              user.stateOfOrigin!,
            ),
            _buildRow(
              'LGA',
              user.lga!,
            ),
            _buildRow(
              'Blood Group',
              user.bloodGroup ?? 'N/A',
            ),
            _buildRow(
              'Genotype',
              user.genotype ?? 'N/A',
            ),
            _buildRow(
              'Religion',
              user.religion ?? 'N/A',
            ),
            _buildRow(
              'Marital Status',
              user.maritalStatus ?? 'N/A',
            ),
          ],
        ),

        const SizedBox(height: 24),

        //Academic Information Section
        _buildSectionHeader('Academic Information', colors.primary),
        Table(
          columnWidths: const {
            0: FlexColumnWidth(0.4),
            1: FlexColumnWidth(0.6),
          },
          border: TableBorder(
            horizontalInside: borderSide,
            verticalInside: borderSide,
          ),
          children: [
            _buildRow(
              'Matric Number',
              user.studentNumber!,
            ),
            _buildRow(
              'Faculty',
              user.faculty!.name,
            ),
            _buildRow(
              'Department',
              user.department!.name,
            ),
            _buildRow(
              'Programme',
              user.programme!.name,
            ),
            _buildRow(
              'Program Duration',
              user.programDuration ?? 'N/A',
            ),
            _buildRow(
              'Admission Type',
              user.admissionType ?? 'N/A',
            ),
            _buildRow(
              'Current Level',
              user.academicLevel!.name,
            ),
            _buildRow(
              'Academic Session',
              user.currentAcademicSessionWithSemesters!.sessionName,
            ),
            _buildRow(
              'Current Semester',
              user.currentAcademicSessionWithSemesters?.semesters.isNotEmpty ==
                      true
                  ? user.currentAcademicSessionWithSemesters!.semesters.first
                          .title ??
                      'N/A'
                  : 'N/A',
            ),
            _buildRow(
              'Cummulative Grade Point Average',
              'N/A',
            ),
          ],
        ),
      ],
    );
  }
}

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:tertiary_mobile/core/constants/assets.dart';
import 'package:tertiary_mobile/core/config/university_config.dart';
import 'package:tertiary_mobile/core/constants/colors.dart';
import 'package:tertiary_mobile/features/authentication/providers/auth_provider.dart';
import 'package:tertiary_mobile/features/courses/presentation/widgets/course_info_row.dart';
import 'package:tertiary_mobile/features/courses/presentation/widgets/course_table.dart';
import 'package:tertiary_mobile/features/courses/providers/courses_provider.dart';
import 'package:tertiary_mobile/shared/presentation/layout/main_app_custom_appbar.dart';

class CourseForm extends ConsumerWidget {
  const CourseForm({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = ref.watch(appColorsProvider);
    final userAsync = ref.watch(authProvider);

    return userAsync.when(
      loading: () => const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      ),
      error: (err, stack) => const Scaffold(
        body: Center(child: Text('Failed to load user')),
      ),
      data: (user) {
        final sessionId = user?.currentAcademicSessionWithSemesters?.id;
        if (user == null || sessionId == null) {
          return const Scaffold(
            body: Center(child: Text('No active session found for user')),
          );
        }

        final groupedCourseRegistrations = ref.watch(
          groupedCourseRegistrationsProvider(sessionId),
        );

        return Scaffold(
          appBar: const CustomAppBar(title: 'Course Form'),
          body: Padding(
            padding: EdgeInsets.all(16.w),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Center(
                    child: Image.asset(Assets.logo, scale: 4),
                  ),
                  SizedBox(height: 20.h),
                  Center(
                    child: Consumer(
                      builder: (context, ref, _) {
                        final universityConfig =
                            ref.watch(universityConfigProvider);
                        return Text(
                          universityConfig.name.toUpperCase(),
                          style: TextStyle(
                            color: colors.primary,
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        );
                      },
                    ),
                  ),
                  SizedBox(height: 30.h),
                  Text(
                    'COURSE FORM',
                    style: TextStyle(
                      color: colors.primary,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 20.h),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // User details column - takes remaining space
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (user.studentNumber != null &&
                                user.studentNumber!.isNotEmpty) ...[
                              InfoRow(
                                label: 'Matriculation Number:',
                                value: user.studentNumber!,
                              ),
                              SizedBox(height: 12.h),
                            ],
                            InfoRow(
                              label: 'Full Name:',
                              value: user.fullname ?? 'User',
                              maxLines: 2,
                            ),
                            if (user.academicLevel?.name != null &&
                                user.academicLevel!.name.isNotEmpty) ...[
                              SizedBox(height: 12.h),
                              InfoRow(
                                label: 'Level:',
                                value: user.academicLevel!.name,
                              ),
                              SizedBox(height: 12.h),
                              InfoRow(
                                label: 'Session:',
                                value: user.currentAcademicSessionWithSemesters
                                        ?.sessionName ??
                                    'N/A',
                              ),
                            ],
                            SizedBox(height: 12.h),
                            if (user.department?.name != null &&
                                user.department!.name.isNotEmpty) ...[
                              InfoRow(
                                label: 'Department:',
                                value: user.department!.name,
                                maxLines: 2,
                              ),
                              SizedBox(height: 12.h),
                            ],
                            if (user.programme?.name != null &&
                                user.programme!.name.isNotEmpty) ...[
                              InfoRow(
                                label: 'Programme:',
                                value: user.programme!.name,
                                maxLines: 2,
                              ),
                              SizedBox(height: 12.h),
                            ],
                          ],
                        ),
                      ),
                      // Profile picture container - fixed size
                      Container(
                        margin: EdgeInsets.only(left: 16.w),
                        height: 100.h,
                        width: 100.w,
                        decoration: BoxDecoration(
                          color: colors.primary,
                          shape: BoxShape.rectangle,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: user.profilePictureUrl != null &&
                                user.profilePictureUrl!.isNotEmpty
                            ? CachedNetworkImage(
                                imageUrl: user.profilePictureUrl!,
                                placeholder: (context, url) =>
                                    const CircularProgressIndicator(),
                                errorWidget: (context, url, error) =>
                                    const Icon(Icons.error),
                                fit: BoxFit.cover,
                              )
                            : const Icon(
                                Icons.person,
                                size: 60,
                                color: Colors.white,
                              ),
                      ),
                    ],
                  ),
                  SizedBox(height: 20.h),
                  groupedCourseRegistrations.when(
                    loading: () =>
                        const Center(child: CircularProgressIndicator()),
                    error: (err, stack) => const Center(
                      child: Text('Error loading courses'),
                    ),
                    data: (groupedCourseRegistrations) {
                      if (groupedCourseRegistrations.isEmpty) {
                        return const Center(
                          child: Text('No registered courses for this session'),
                        );
                      }
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          for (int i = 0;
                              i < groupedCourseRegistrations.entries.length;
                              i++) ...[
                            Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.h),
                              child: Text(
                                groupedCourseRegistrations.entries
                                    .elementAt(i)
                                    .key,
                                style: TextStyle(
                                  color: colors.primary,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                            CourseTable(
                              items: groupedCourseRegistrations.entries
                                  .elementAt(i)
                                  .value,
                              columns: const [
                                'code',
                                'title',
                                'credit',
                                'status',
                                'regStatus',
                              ],
                            ),
                            if (i !=
                                groupedCourseRegistrations.entries.length - 1)
                              SizedBox(height: 24.h),
                          ]
                        ],
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

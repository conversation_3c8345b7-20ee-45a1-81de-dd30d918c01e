import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:tertiary_mobile/core/constants/colors.dart';
import 'package:tertiary_mobile/features/authentication/providers/auth_provider.dart';

import 'package:tertiary_mobile/features/courses/domain/course_table_item.dart';
import 'package:tertiary_mobile/features/courses/presentation/widgets/course_table.dart';
import 'package:tertiary_mobile/features/courses/presentation/widgets/course_table_skeleton.dart';
import 'package:tertiary_mobile/features/courses/providers/course_registration_provider.dart';
import 'package:tertiary_mobile/routing/router.dart';
import 'package:tertiary_mobile/shared/presentation/layout/main_app_custom_appbar.dart';
import 'package:tertiary_mobile/shared/presentation/widgets/session_dropdown.dart';
import '../../providers/selected_session_provider.dart';

class CourseRegistrationScreen extends HookConsumerWidget {
  const CourseRegistrationScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = ref.watch(appColorsProvider);
    final router = ref.read(routerProvider);
    final selectedSession = ref.watch(selectedSessionProvider);
    final scrollController = useScrollController();
    final user = ref.watch(authProvider).maybeWhen(
          data: (user) => user,
          orElse: () => null,
        );

    final notifier = selectedSession != null
        ? ref.read(courseRegistrationNotifierProvider(selectedSession).notifier)
        : null;

    // Track the registration provider state to detect loading during session changes
    final registrationState = selectedSession != null
        ? ref.watch(courseRegistrationNotifierProvider(selectedSession))
        : const AsyncValue<CourseRegistrationState>.loading();

    // UI header and dropdown always visible
    return Scaffold(
      appBar: const CustomAppBar(title: 'Course Registration'),
      body: Padding(
        padding: EdgeInsets.all(12.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Matric No:',
                        style: TextStyle(
                          color: colors.primary,
                          fontWeight: FontWeight.bold,
                        )),
                    Text(
                      user?.studentNumber ?? 'N/A',
                      style: TextStyle(
                        color: colors.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: SessionDropdown(
                    selectedSessionId: selectedSession,
                    onChanged: (sessionId) async {
                      await ref
                          .read(selectedSessionProvider.notifier)
                          .setSession(sessionId);
                    },
                  ),
                ),
              ],
            ),
            SizedBox(height: 20.h),
            // Only the table area is replaced by loading/error/data
            Expanded(
              child: registrationState.when(
                loading: () => const CourseTableSkeleton(
                  columns: ['code', 'title', 'credit', 'options'],
                ),
                error: (err, stack) => RefreshIndicator(
                  onRefresh: () async {
                    if (selectedSession != null) {
                      await ref
                          .read(courseRegistrationNotifierProvider(
                                  selectedSession)
                              .notifier)
                          .refresh(selectedSession);
                    }
                  },
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: SizedBox(
                      height: MediaQuery.of(context).size.height * 0.6,
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.error_outline,
                              size: 48,
                              color: Colors.red,
                            ),
                            SizedBox(height: 16.h),
                            Text(
                              'Error loading courses',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.red,
                              ),
                            ),
                            SizedBox(height: 8.h),
                            Text(
                              'Unable to load course specifications\nCheck your connection and pull to refresh',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                data: (registrationData) {
                  // Group specifications by semester
                  final Map<String, List<CourseTableItem>> groupedSpecs = {};
                  for (final spec in registrationData.specifications) {
                    final sem = spec.session?.semesters.first.title ??
                        spec.session?.semesters.first.accronym ??
                        'Semester';
                    groupedSpecs.putIfAbsent(sem, () => []).add(spec);
                  }

                  // Check if we have specifications to display
                  if (groupedSpecs.isEmpty) {
                    return RefreshIndicator(
                      onRefresh: () async {
                        if (selectedSession != null) {
                          await ref
                              .read(courseRegistrationNotifierProvider(
                                      selectedSession)
                                  .notifier)
                              .refresh(selectedSession);
                        }
                      },
                      child: SingleChildScrollView(
                        physics: const AlwaysScrollableScrollPhysics(),
                        child: SizedBox(
                          height: MediaQuery.of(context).size.height * 0.6,
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.library_books_outlined,
                                  size: 48,
                                  color: Colors.grey[400],
                                ),
                                SizedBox(height: 16.h),
                                Text(
                                  'No courses available',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.grey[700],
                                  ),
                                ),
                                SizedBox(height: 8.h),
                                Text(
                                  'No course specifications found for this session\nPull to refresh or try another session',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  }

                  return RefreshIndicator(
                    onRefresh: () async {
                      if (selectedSession != null) {
                        await ref
                            .read(courseRegistrationNotifierProvider(
                                    selectedSession)
                                .notifier)
                            .refresh(selectedSession);
                      }
                    },
                    child: ListView(
                      key: const PageStorageKey('course_registration_list'),
                      controller: scrollController,
                      physics: const AlwaysScrollableScrollPhysics(),
                      children: [
                        for (var entry in groupedSpecs.entries) ...[
                          Padding(
                            padding: EdgeInsets.only(bottom: 12.h, top: 8.h),
                            child: Text(
                              '${entry.key} Courses',
                              style: TextStyle(
                                color: colors.primary,
                                fontWeight: FontWeight.bold,
                                fontSize: 16.sp,
                              ),
                            ),
                          ),
                          CourseTable(
                            key: ValueKey('course_table_${entry.key}'),
                            items: entry.value,
                            columns: const [
                              'code',
                              'title',
                              'credit',
                              'options'
                            ],
                            onViewInfo: (item) {
                              router.pushNamed('courseinfo', extra: item);
                            },
                            onAddCourse: (item) {
                              notifier?.addCourse(item);
                            },
                            onRemoveCourse: (item) {
                              notifier?.removeCourse(item);
                            },
                            rowColorBuilder: (item) {
                              final isSelected = item.isRegistered;
                              return isSelected
                                  ? colors.primary.withValues(alpha: 0.1)
                                  : null;
                            },
                          ),
                          if (entry.key != groupedSpecs.keys.last)
                            SizedBox(height: 24.h),
                        ]
                      ],
                    ),
                  );
                },
              ),
            ),
            SizedBox(height: 8.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                registrationState.when(
                  data: (regData) {
                    final totalUnits = regData.specifications
                        .where((item) => item.isRegistered)
                        .fold<int>(0, (sum, c) => sum + c.creditUnit);
                    return Text(
                      'Total Units: $totalUnits',
                      style: TextStyle(
                        color: colors.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    );
                  },
                  loading: () => const SizedBox.shrink(),
                  error: (err, stack) => const SizedBox.shrink(),
                ),
                selectedSession != null
                    ? ref
                        .watch(
                            courseRegistrationNotifierProvider(selectedSession))
                        .when(
                          data: (regState) => ElevatedButton(
                            onPressed: () => router.pushNamed(
                              'course-summary',
                              extra: regState.selected,
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: colors.primary,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                              padding: EdgeInsets.symmetric(
                                horizontal: 12.w,
                                vertical: 10.h,
                              ),
                            ),
                            child: Text(
                              'Submit',
                              style: TextStyle(color: Colors.white),
                            ),
                          ),
                          loading: () => const SizedBox.shrink(),
                          error: (err, stack) => const SizedBox.shrink(),
                        )
                    : const SizedBox.shrink(),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

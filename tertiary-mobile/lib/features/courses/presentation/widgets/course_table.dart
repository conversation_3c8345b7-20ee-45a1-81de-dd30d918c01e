import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:tertiary_mobile/core/constants/colors.dart';
import 'package:tertiary_mobile/features/courses/domain/course_table_item.dart';
import 'package:tertiary_mobile/features/courses/data/models/course_registration_model.dart'; // Keep for backward compatibility

enum CourseAction {
  viewInfo,
  addCourse,
  removeCourse,
  viewMaterial,
}

typedef CourseOptionsBuilder = Widget Function(
  BuildContext context,
  CourseTableItem item,
);

typedef RowColorBuilder = Color? Function(CourseTableItem item);

class CourseTable extends ConsumerWidget {
  final List<CourseTableItem> items;
  final List<String> columns;
  final CourseOptionsBuilder? optionsBuilder;
  final RowColorBuilder? rowColorBuilder;

  // Convenience handlers for legacy usage
  final void Function(CourseTableItem item)? onViewInfo;
  final void Function(CourseTableItem item)? onAddCourse;
  final void Function(CourseTableItem item)? onRemoveCourse;
  final void Function(CourseTableItem item)? onViewMaterial;

  const CourseTable({
    super.key,
    required this.items,
    this.columns = const ['code', 'title', 'credit', 'options'],
    this.optionsBuilder,
    this.onViewInfo,
    this.onAddCourse,
    this.onRemoveCourse,
    this.onViewMaterial,
    this.rowColorBuilder,
  });

  // For backward compatibility
  factory CourseTable.fromRegistrations({
    Key? key,
    required List<CourseRegistration> registrations,
    List<String> columns = const ['code', 'title', 'credit', 'options'],
    CourseOptionsBuilder? optionsBuilder,
    void Function(CourseTableItem item)? onViewInfo,
    void Function(CourseTableItem item)? onAddCourse,
    void Function(CourseTableItem item)? onRemoveCourse,
    void Function(CourseTableItem item)? onViewMaterial,
    RowColorBuilder? rowColorBuilder,
  }) {
    return CourseTable(
      key: key,
      items: registrations.map((r) => r.toTableItem()).toList(),
      columns: columns,
      optionsBuilder: optionsBuilder,
      onViewInfo: onViewInfo,
      onAddCourse: onAddCourse,
      onRemoveCourse: onRemoveCourse,
      onViewMaterial: onViewMaterial,
      rowColorBuilder: rowColorBuilder,
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = ref.watch(appColorsProvider);

    final Map<String, double> columnFlex = {
      'code': 1.2,
      'title': 3.0,
      'credit': 1.0,
      'status': 1.0,
      'regStatus': 1.4,
      'options': 1.0,
    };

    final tableColumnWidths = <int, TableColumnWidth>{};
    for (var i = 0; i < columns.length; i++) {
      final col = columns[i];
      if (columnFlex.containsKey(col)) {
        tableColumnWidths[i] = FlexColumnWidth(columnFlex[col]!);
      } else {
        tableColumnWidths[i] = const FlexColumnWidth(1.0);
      }
    }

    // Build header row based on columns
    List<Widget> buildHeader() {
      return columns.map((col) {
        switch (col) {
          case 'code':
            return _buildHeaderCell('Course\nCode');
          case 'title':
            return _buildHeaderCell('Course Title');
          case 'credit':
            return _buildHeaderCell('Credit\nUnit');
          case 'status':
            return _buildHeaderCell('Status');
          case 'regStatus':
            return _buildHeaderCell('Reg.\nStatus');
          case 'options':
            return _buildHeaderCell('Options');
          default:
            return const SizedBox.shrink();
        }
      }).toList();
    }

    // Build data row for each item
    List<TableRow> buildRows() {
      return List.generate(items.length, (i) {
        final item = items[i];
        final rowColor = rowColorBuilder?.call(item);

        return TableRow(
          decoration: BoxDecoration(color: rowColor),
          children: columns.map((col) {
            switch (col) {
              case 'code':
                final code = item.courseCode;
                String first = code.length >= 3 ? code.substring(0, 3) : code;
                String last =
                    code.length > 3 ? code.substring(code.length - 3) : '';
                final formatted = last.isNotEmpty ? '$first\n$last' : first;
                return _buildCell(formatted);
              case 'title':
                return _buildCell(item.courseTitle);
              case 'credit':
                return _buildCell(item.creditUnit.toString());
              case 'status':
                final status = item.formattedCourseType;
                return _buildCell(status);
              case 'regStatus':
                final status = item.approvalStatus ?? 'N/A';
                return _buildCell(status);
              case 'options':
                if (optionsBuilder != null) {
                  return optionsBuilder!(context, item);
                } else if (onViewInfo != null ||
                    onAddCourse != null ||
                    onRemoveCourse != null ||
                    onViewMaterial != null) {
                  return _defaultOptionsCell(context, ref, item);
                } else {
                  return const SizedBox.shrink();
                }
              default:
                return const SizedBox.shrink();
            }
          }).toList(),
        );
      });
    }

    return Table(
      columnWidths: tableColumnWidths,
      border: TableBorder(
        horizontalInside: const BorderSide(
          color: Color.fromRGBO(217, 217, 217, 1),
          width: 1,
        ),
      ),
      children: [
        TableRow(
          decoration: BoxDecoration(color: colors.primary),
          children: buildHeader(),
        ),
        ...buildRows(),
      ],
    );
  }

  Widget _defaultOptionsCell(
    BuildContext context,
    WidgetRef ref,
    CourseTableItem item,
  ) {
    final colors = ref.watch(appColorsProvider);
    final items = <PopupMenuEntry<CourseAction>>[];

    if (onViewInfo != null) {
      items.add(const PopupMenuItem(
        value: CourseAction.viewInfo,
        child: Text('Course Info'),
      ));
    }
    if (onAddCourse != null && !item.isRegistered) {
      items.add(const PopupMenuItem(
        value: CourseAction.addCourse,
        child: Text('Add Course'),
      ));
    }
    if (onRemoveCourse != null && item.isRegistered) {
      items.add(const PopupMenuItem(
        value: CourseAction.removeCourse,
        child: Text('Drop Course'),
      ));
    }
    if (onViewMaterial != null) {
      items.add(const PopupMenuItem(
        value: CourseAction.viewMaterial,
        child: Text('View Material'),
      ));
    }

    return items.isEmpty
        ? const SizedBox.shrink()
        : PopupMenuButton<CourseAction>(
            icon: Icon(Icons.more_vert, color: colors.primary),
            onSelected: (action) {
              switch (action) {
                case CourseAction.viewInfo:
                  onViewInfo?.call(item);
                  break;
                case CourseAction.addCourse:
                  onAddCourse?.call(item);
                  break;
                case CourseAction.removeCourse:
                  onRemoveCourse?.call(item);
                  break;
                case CourseAction.viewMaterial:
                  onViewMaterial?.call(item);
                  break;
              }
            },
            itemBuilder: (context) => items,
          );
  }

  Widget _buildHeaderCell(String text) => Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
        child: Center(
          child: Text(
            text,
            textAlign: TextAlign.center,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ),
      );

  Widget _buildCell(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.black,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
        textAlign: TextAlign.center,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }
}

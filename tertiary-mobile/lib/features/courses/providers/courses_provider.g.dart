// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'courses_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$groupedCourseRegistrationsHash() =>
    r'5e098984fb76843e2182d80431d1d5a8802d6bb6';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [groupedCourseRegistrations].
@ProviderFor(groupedCourseRegistrations)
const groupedCourseRegistrationsProvider = GroupedCourseRegistrationsFamily();

/// See also [groupedCourseRegistrations].
class GroupedCourseRegistrationsFamily
    extends Family<AsyncValue<Map<String, List<CourseTableItem>>>> {
  /// See also [groupedCourseRegistrations].
  const GroupedCourseRegistrationsFamily();

  /// See also [groupedCourseRegistrations].
  GroupedCourseRegistrationsProvider call(
    int? sessionId,
  ) {
    return GroupedCourseRegistrationsProvider(
      sessionId,
    );
  }

  @override
  GroupedCourseRegistrationsProvider getProviderOverride(
    covariant GroupedCourseRegistrationsProvider provider,
  ) {
    return call(
      provider.sessionId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'groupedCourseRegistrationsProvider';
}

/// See also [groupedCourseRegistrations].
class GroupedCourseRegistrationsProvider
    extends AutoDisposeFutureProvider<Map<String, List<CourseTableItem>>> {
  /// See also [groupedCourseRegistrations].
  GroupedCourseRegistrationsProvider(
    int? sessionId,
  ) : this._internal(
          (ref) => groupedCourseRegistrations(
            ref as GroupedCourseRegistrationsRef,
            sessionId,
          ),
          from: groupedCourseRegistrationsProvider,
          name: r'groupedCourseRegistrationsProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$groupedCourseRegistrationsHash,
          dependencies: GroupedCourseRegistrationsFamily._dependencies,
          allTransitiveDependencies:
              GroupedCourseRegistrationsFamily._allTransitiveDependencies,
          sessionId: sessionId,
        );

  GroupedCourseRegistrationsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.sessionId,
  }) : super.internal();

  final int? sessionId;

  @override
  Override overrideWith(
    FutureOr<Map<String, List<CourseTableItem>>> Function(
            GroupedCourseRegistrationsRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: GroupedCourseRegistrationsProvider._internal(
        (ref) => create(ref as GroupedCourseRegistrationsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        sessionId: sessionId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Map<String, List<CourseTableItem>>>
      createElement() {
    return _GroupedCourseRegistrationsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is GroupedCourseRegistrationsProvider &&
        other.sessionId == sessionId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, sessionId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin GroupedCourseRegistrationsRef
    on AutoDisposeFutureProviderRef<Map<String, List<CourseTableItem>>> {
  /// The parameter `sessionId` of this provider.
  int? get sessionId;
}

class _GroupedCourseRegistrationsProviderElement
    extends AutoDisposeFutureProviderElement<Map<String, List<CourseTableItem>>>
    with GroupedCourseRegistrationsRef {
  _GroupedCourseRegistrationsProviderElement(super.provider);

  @override
  int? get sessionId =>
      (origin as GroupedCourseRegistrationsProvider).sessionId;
}

String _$coursesNotifierHash() => r'dec4170b8fa619808fc30e4bffa35e61bc39d6f3';

abstract class _$CoursesNotifier
    extends BuildlessAutoDisposeAsyncNotifier<List<CourseRegistration>> {
  late final int? sessionId;

  FutureOr<List<CourseRegistration>> build(
    int? sessionId,
  );
}

/// See also [CoursesNotifier].
@ProviderFor(CoursesNotifier)
const coursesNotifierProvider = CoursesNotifierFamily();

/// See also [CoursesNotifier].
class CoursesNotifierFamily
    extends Family<AsyncValue<List<CourseRegistration>>> {
  /// See also [CoursesNotifier].
  const CoursesNotifierFamily();

  /// See also [CoursesNotifier].
  CoursesNotifierProvider call(
    int? sessionId,
  ) {
    return CoursesNotifierProvider(
      sessionId,
    );
  }

  @override
  CoursesNotifierProvider getProviderOverride(
    covariant CoursesNotifierProvider provider,
  ) {
    return call(
      provider.sessionId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'coursesNotifierProvider';
}

/// See also [CoursesNotifier].
class CoursesNotifierProvider extends AutoDisposeAsyncNotifierProviderImpl<
    CoursesNotifier, List<CourseRegistration>> {
  /// See also [CoursesNotifier].
  CoursesNotifierProvider(
    int? sessionId,
  ) : this._internal(
          () => CoursesNotifier()..sessionId = sessionId,
          from: coursesNotifierProvider,
          name: r'coursesNotifierProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$coursesNotifierHash,
          dependencies: CoursesNotifierFamily._dependencies,
          allTransitiveDependencies:
              CoursesNotifierFamily._allTransitiveDependencies,
          sessionId: sessionId,
        );

  CoursesNotifierProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.sessionId,
  }) : super.internal();

  final int? sessionId;

  @override
  FutureOr<List<CourseRegistration>> runNotifierBuild(
    covariant CoursesNotifier notifier,
  ) {
    return notifier.build(
      sessionId,
    );
  }

  @override
  Override overrideWith(CoursesNotifier Function() create) {
    return ProviderOverride(
      origin: this,
      override: CoursesNotifierProvider._internal(
        () => create()..sessionId = sessionId,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        sessionId: sessionId,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<CoursesNotifier,
      List<CourseRegistration>> createElement() {
    return _CoursesNotifierProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CoursesNotifierProvider && other.sessionId == sessionId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, sessionId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CoursesNotifierRef
    on AutoDisposeAsyncNotifierProviderRef<List<CourseRegistration>> {
  /// The parameter `sessionId` of this provider.
  int? get sessionId;
}

class _CoursesNotifierProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<CoursesNotifier,
        List<CourseRegistration>> with CoursesNotifierRef {
  _CoursesNotifierProviderElement(super.provider);

  @override
  int? get sessionId => (origin as CoursesNotifierProvider).sessionId;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package

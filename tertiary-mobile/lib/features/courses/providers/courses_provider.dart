import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:tertiary_mobile/features/courses/data/models/course_registration_model.dart';
import 'package:tertiary_mobile/features/courses/domain/course_table_item.dart';
import '../data/repositories/courses_repository.dart';

part 'courses_provider.g.dart';

@riverpod
class CoursesNotifier extends _$CoursesNotifier {
  @override
  Future<List<CourseRegistration>> build(int? sessionId) async {
    // If no session is selected, return empty list
    if (sessionId == null) {
      return [];
    }

    // Get cached data first for immediate display
    final repository = ref.read(coursesRepositoryProvider);
    final cachedRegistrations =
        await repository.getCachedCourseRegistrations(sessionId);

    // Check if this is a session change by comparing with previous session
    final currentState = state.valueOrNull;
    final previousSessionId = currentState?.firstOrNull?.session?.id;
    final isSessionChange =
        previousSessionId != null && previousSessionId != sessionId;

    // If this is a session change, fetch fresh data immediately to avoid showing wrong data
    if (isSessionChange) {
      try {
        return await repository.fetchCourseRegistrations(sessionId);
      } catch (_) {
        // If fetch fails, try cached data as fallback
        return cachedRegistrations;
      }
    }

    // For initial load or same session, use cached data if available
    if (cachedRegistrations.isNotEmpty) {
      // Start background refresh without blocking the UI
      _refreshInBackground(sessionId);
      return cachedRegistrations;
    }

    // If no cached data, fall back to traditional fetch
    return await repository.fetchCourseRegistrations(sessionId);
  }

  Future<void> _refreshInBackground(int? sessionId) async {
    final repository = ref.read(coursesRepositoryProvider);

    try {
      // Fetch fresh data in background
      final freshRegistrations =
          await repository.fetchCourseRegistrations(sessionId);

      // Try to update state, but handle disposal gracefully
      try {
        state = AsyncValue.data(freshRegistrations);
      } catch (_) {
        // Provider was disposed during background refresh, ignore
      }
    } catch (error) {
      // Silently fail background refresh - user still sees cached data
      // Could add logging here if needed
    }
  }

  Future<void> refresh(int? sessionId) async {
    // For manual refresh, show loading state and fetch fresh data
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(
      () async {
        final repository = ref.read(coursesRepositoryProvider);
        return await repository.fetchCourseRegistrations(sessionId);
      },
    );
  }
}

@riverpod
Future<Map<String, List<CourseTableItem>>> groupedCourseRegistrations(
  Ref ref,
  int? sessionId,
) async {
  ref.keepAlive();
  final regState = await ref.watch(coursesNotifierProvider(sessionId).future);
  // Group registrations by semester title or semester accronym
  final Map<String, List<CourseTableItem>> grouped = {};
  for (final reg in regState) {
    final sem = reg.semesterTitle ?? reg.semesterAccronym ?? 'Semester';
    final tableItem = reg.toTableItem();
    grouped.putIfAbsent(sem, () => []).add(tableItem);
  }
  return grouped;
}

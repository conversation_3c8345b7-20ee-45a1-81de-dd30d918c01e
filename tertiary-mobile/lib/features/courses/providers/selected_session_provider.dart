import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:tertiary_mobile/core/utils/shared_preferences.dart';

part 'selected_session_provider.g.dart';

@riverpod
class SelectedSession extends _$SelectedSession {
  static const String _selectedSessionKey = 'selected_session_id';

  @override
  int? build() {
    // Load the last selected session from storage
    _loadSelectedSession();
    return null;
  }

  Future<void> _loadSelectedSession() async {
    try {
      final sessionId = SharedPreferencesService.getInt(_selectedSessionKey);
      if (sessionId != null) {
        state = sessionId;
      }
    } catch (e) {
      // Ignore errors and use default null state
    }
  }

  Future<void> setSession(int? sessionId) async {
    state = sessionId;

    // Persist the selected session
    try {
      if (sessionId != null) {
        await SharedPreferencesService.setInt(_selectedSessionKey, sessionId);
      } else {
        await SharedPreferencesService.remove(_selectedSessionKey);
      }
    } catch (e) {
      // Ignore storage errors, the session is still set in memory
    }
  }
}

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:tertiary_mobile/core/utils/logger.dart';
import 'package:tertiary_mobile/features/courses/domain/course_table_item.dart';
import 'package:tertiary_mobile/features/courses/data/repositories/courses_repository.dart';
import 'package:tertiary_mobile/features/courses/data/models/course_specification_model.dart';

part 'course_registration_provider.g.dart';

class CourseRegistrationState {
  final List<CourseTableItem> selected;
  final List<CourseTableItem> specifications;
  final bool isSubmitting;
  final String? submissionError;

  CourseRegistrationState({
    this.selected = const [],
    this.specifications = const [],
    this.isSubmitting = false,
    this.submissionError,
  });

  CourseRegistrationState copyWith({
    List<CourseTableItem>? selected,
    List<CourseTableItem>? specifications,
    bool? isSubmitting,
    String? submissionError,
  }) {
    return CourseRegistrationState(
      selected: selected ?? this.selected,
      specifications: specifications ?? this.specifications,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      submissionError: submissionError,
    );
  }
}

@riverpod
class CourseRegistrationNotifier extends _$CourseRegistrationNotifier {
  @override
  Future<CourseRegistrationState> build([int? sessionId]) async {
    if (sessionId == null) {
      return CourseRegistrationState(
        selected: [],
        specifications: [],
      );
    }

    final repository = ref.read(coursesRepositoryProvider);

    // Get the current state to check for selected courses
    final currentState = state.valueOrNull;
    final selectedCourseIds =
        currentState?.selected.map((c) => c.sourceId).toSet() ?? <String>{};

    // Check if this is a session change by comparing with previous session
    final previousSessionId =
        currentState?.specifications.firstOrNull?.session?.id;
    final isSessionChange =
        previousSessionId != null && previousSessionId != sessionId;

    // If this is a session change, fetch fresh data immediately to avoid showing wrong data
    if (isSessionChange) {
      try {
        final specs = await repository.fetchCourseSpecifications(sessionId);
        final tableItems = specs.map((spec) {
          final item = spec.toTableItem();
          final isSelected =
              selectedCourseIds.any((id) => id == spec.id.toString());
          return isSelected ? item.copyWith(isRegistered: true) : item;
        }).toList();

        return CourseRegistrationState(
          selected: currentState?.selected ?? [],
          specifications: tableItems,
        );
      } catch (_) {
        // If fetch fails, return empty state
        return CourseRegistrationState(
          selected: currentState?.selected ?? [],
          specifications: [],
        );
      }
    }

    // For initial load or same session, try cached data first
    var cachedSpecs = <CourseSpecification>[];
    try {
      cachedSpecs = await repository.getCachedCourseSpecifications(sessionId);
    } catch (_) {
      // Ignore cache errors
    }

    // If we have cached data, show it immediately and start background refresh
    if (cachedSpecs.isNotEmpty) {
      final cachedTableItems = cachedSpecs.map((spec) {
        final item = spec.toTableItem();
        final isSelected =
            selectedCourseIds.any((id) => id.toString() == spec.id.toString());
        return isSelected ? item.copyWith(isRegistered: true) : item;
      }).toList();

      // Start background refresh without blocking the UI
      _refreshInBackground(sessionId);

      return CourseRegistrationState(
        selected: currentState?.selected ?? [],
        specifications: cachedTableItems,
      );
    }

    // No cached data - try to fetch fresh data
    // Use multiple fallback strategies to avoid errors
    var specs = <CourseSpecification>[];

    // Strategy 1: Try normal fetch
    try {
      specs = await repository.fetchCourseSpecifications(sessionId);
    } catch (_) {
      // Strategy 2: Try cached data again (in case it was just added)
      try {
        specs = await repository.getCachedCourseSpecifications(sessionId);
      } catch (_) {
        // Strategy 3: Return empty state rather than error
        return CourseRegistrationState(
          selected: currentState?.selected ?? [],
          specifications: [],
        );
      }
    }

    // Convert specs to table items
    final tableItems = specs.map((spec) {
      final item = spec.toTableItem();
      final isSelected =
          selectedCourseIds.any((id) => id.toString() == spec.id.toString());
      return isSelected ? item.copyWith(isRegistered: true) : item;
    }).toList();

    return CourseRegistrationState(
      selected: currentState?.selected ?? [],
      specifications: tableItems,
    );
  }

  Future<void> _refreshInBackground(int? sessionId) async {
    final repository = ref.read(coursesRepositoryProvider);

    try {
      // Fetch fresh data in background
      final freshSpecs = await repository.fetchCourseSpecifications(sessionId);

      // Get current state to preserve selected courses
      final currentState = state.valueOrNull;
      if (currentState == null) return;

      final selectedCourseIds =
          currentState.selected.map((c) => c.sourceId).toSet();

      // Convert fresh specs to table items
      final freshTableItems = freshSpecs.map((spec) {
        final item = spec.toTableItem();
        final isSelected =
            selectedCourseIds.any((id) => id == spec.id.toString());
        return isSelected ? item.copyWith(isRegistered: true) : item;
      }).toList();

      // Update state with fresh data when available
      state = AsyncValue.data(currentState.copyWith(
        specifications: freshTableItems,
      ));
    } catch (error) {
      // Silently fail background refresh - user still sees cached data
      // Could add logging here if needed
    }
  }

  void addCourse(CourseTableItem item) {
    final current = state.value;
    if (current == null) return;

    final before = current.selected;
    if (before.any((c) => c.id == item.id)) return;

    // Mark the item as registered
    final updatedItem = item.copyWith(isRegistered: true);

    // Update the specifications to reflect the change
    final updatedSpecs = current.specifications.map((spec) {
      return spec.id == item.id ? updatedItem : spec;
    }).toList();

    state = AsyncValue.data(
      current.copyWith(
        selected: [...before, updatedItem],
        specifications: updatedSpecs,
      ),
    );
  }

  void removeCourse(CourseTableItem item) {
    final current = state.value;
    if (current == null) return;

    final before = current.selected;
    final filtered = before.where((c) => c.id != item.id).toList();

    // Mark the item as not registered in specifications
    final updatedSpecs = current.specifications.map((spec) {
      return spec.id == item.id ? spec.copyWith(isRegistered: false) : spec;
    }).toList();

    state = AsyncValue.data(
      current.copyWith(
        selected: filtered,
        specifications: updatedSpecs,
      ),
    );
  }

  Future<void> submitRegistration() async {
    final current = state.value;
    logger.i('Submitting registration: $current');
    if (current == null || current.isSubmitting) return;

    try {
      // Set submitting state
      state = AsyncValue.data(current.copyWith(
        isSubmitting: true,
        submissionError: null,
      ));

      // Transform selected courses to the required format
      final registrations = current.selected
          .map((item) => {
                'course_id': int.tryParse(item.sourceId) ?? 0,
                'semester_id': item.session?.semesters.isNotEmpty == true
                    ? item.session!.semesters.first.id
                    : null,
                'session_id': item.session?.id,
              })
          .where((reg) => reg['course_id'] != 0 && reg['semester_id'] != null)
          .toList();
      logger.e('Submitting registrations: $registrations');
      // Call repository
      await ref
          .read(coursesRepositoryProvider)
          .submitCourseRegistrations(registrations);

      // Clear selected courses on success
      state = AsyncValue.data(current.copyWith(
        selected: [],
        isSubmitting: false,
      ));
    } catch (e) {
      state = AsyncValue.data(current.copyWith(
        isSubmitting: false,
        submissionError: e.toString(),
      ));
      rethrow;
    }
  }

  Future<void> refresh(int? sessionId) async {
    if (sessionId == null) {
      state = AsyncValue.data(CourseRegistrationState());
      return;
    }

    // For manual refresh, show loading state and fetch fresh data
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      final repository = ref.read(coursesRepositoryProvider);
      final specs = await repository.fetchCourseSpecifications(sessionId);

      // Get current state to preserve selected courses
      final currentState = state.valueOrNull;
      final selectedCourseIds =
          currentState?.selected.map((c) => c.sourceId).toSet() ?? <String>{};

      // Convert specs to table items and set isRegistered based on selected courses
      final tableItems = specs.map((spec) {
        final item = spec.toTableItem();
        final isSelected =
            selectedCourseIds.any((id) => id == spec.id.toString());
        return isSelected ? item.copyWith(isRegistered: true) : item;
      }).toList();

      return CourseRegistrationState(
        selected: currentState?.selected ?? [],
        specifications: tableItems,
      );
    });
  }
}

@riverpod
Future<Map<String, List<CourseTableItem>>> groupedCourseSpecifications(
  Ref ref,
  int? sessionId,
) async {
  ref.keepAlive();
  final state =
      await ref.watch(courseRegistrationNotifierProvider(sessionId).future);

  final Map<String, List<CourseTableItem>> grouped = {};
  for (final spec in state.specifications) {
    final semesters = spec.session?.semesters ?? [];
    final sem = semesters.isNotEmpty
        ? (semesters.first.title ?? semesters.first.accronym ?? 'Semester')
        : 'Semester';
    grouped.putIfAbsent(sem, () => []).add(spec);
  }

  return grouped;
}

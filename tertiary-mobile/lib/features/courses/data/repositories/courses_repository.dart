import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:tertiary_mobile/core/network/providers/internet_checker.dart';
import 'package:tertiary_mobile/features/courses/data/models/course_specification_model.dart';
import '../models/course_registration_model.dart';
import '../sources/remote_courses_source.dart';
import '../sources/local_courses_source.dart';
import '../sources/local_course_specifications_source.dart';

part 'courses_repository.g.dart';

@riverpod
CoursesRepository coursesRepository(Ref ref) {
  final remote = ref.read(remoteCoursesSourceProvider);
  final localRegistrations = ref.read(localCourseRegistrationsSourceProvider);
  final localSpecs = ref.read(localCourseSpecificationsSourceProvider);
  final internetChecker = ref.read(internetCheckerProvider);
  return CoursesRepository(
      remote, localRegistrations, localSpecs, internetChecker);
}

class CoursesRepository {
  final RemoteCoursesSource remote;
  final LocalCourseRegistrationsSource localRegistrations;
  final LocalCourseSpecificationsSource localSpecs;
  final InternetConnectionChecker internetChecker;

  CoursesRepository(
    this.remote,
    this.localRegistrations,
    this.localSpecs,
    this.internetChecker,
  );

  Future<List<CourseRegistration>> fetchCourseRegistrations(
      int? sessionId) async {
    final hasConnection = await internetChecker.hasConnection;

    if (hasConnection) {
      try {
        // Check if cache needs refresh to avoid unnecessary network calls
        final needsRefresh = await localRegistrations.needsRefresh(sessionId);
        if (!needsRefresh) {
          final cached =
              await localRegistrations.getCourseRegistrations(sessionId);
          if (cached.isNotEmpty) {
            return cached;
          }
        }

        final regs = await remote.fetchCourseRegistrations(sessionId);
        await localRegistrations.saveCourseRegistrations(regs);
        return regs;
      } catch (_) {
        return await localRegistrations.getCourseRegistrations(sessionId);
      }
    } else {
      return await localRegistrations.getCourseRegistrations(sessionId);
    }
  }

  Future<List<CourseSpecification>> fetchCourseSpecifications(
      int? sessionId) async {
    final hasConnection = await internetChecker.hasConnection;
    if (hasConnection) {
      try {
        final specs = await remote.fetchCourseSpecifications(sessionId);
        await localSpecs.saveCourseSpecifications(specs);
        return specs;
      } catch (_) {
        return await localSpecs.getCourseSpecifications(sessionId);
      }
    } else {
      return await localSpecs.getCourseSpecifications(sessionId);
    }
  }

  Future<List<CourseRegistration>> getCachedCourseRegistrations(
      int? sessionId) async {
    return localRegistrations.getCourseRegistrations(sessionId);
  }

  Future<List<CourseSpecification>> getCachedCourseSpecifications(
      int? sessionId) async {
    return localSpecs.getCourseSpecifications(sessionId);
  }

  /// Implements stale-while-revalidate pattern for course registrations:
  /// 1. Returns cached data immediately if available
  /// 2. Triggers background refresh if online
  /// 3. Returns fresh data when background refresh completes
  Stream<List<CourseRegistration>> fetchCourseRegistrationsStaleWhileRevalidate(
      int? sessionId) async* {
    // First, yield cached data immediately if available
    final cachedRegistrations =
        await localRegistrations.getCourseRegistrations(sessionId);
    if (cachedRegistrations.isNotEmpty) {
      yield cachedRegistrations;
    }

    // Then attempt to fetch fresh data in the background
    final hasConnection = await internetChecker.hasConnection;
    if (hasConnection) {
      try {
        final freshRegistrations =
            await remote.fetchCourseRegistrations(sessionId);
        await localRegistrations.saveCourseRegistrations(freshRegistrations);
        yield freshRegistrations;
      } catch (_) {
        // If fresh fetch fails and we haven't yielded cached data yet, yield it now
        if (cachedRegistrations.isEmpty) {
          final fallbackRegistrations =
              await localRegistrations.getCourseRegistrations(sessionId);
          yield fallbackRegistrations;
        }
        // If we already yielded cached data, we don't need to do anything
        // The UI will continue showing the cached data
      }
    } else if (cachedRegistrations.isEmpty) {
      // No connection and no cached data - yield empty list
      yield <CourseRegistration>[];
    }
  }

  /// Implements stale-while-revalidate pattern for course specifications:
  /// 1. Returns cached data immediately if available
  /// 2. Triggers background refresh if online
  /// 3. Returns fresh data when background refresh completes
  Stream<List<CourseSpecification>>
      fetchCourseSpecificationsStaleWhileRevalidate(int? sessionId) async* {
    // First, yield cached data immediately if available
    final cachedSpecifications =
        await localSpecs.getCourseSpecifications(sessionId);
    if (cachedSpecifications.isNotEmpty) {
      yield cachedSpecifications;
    }

    // Then attempt to fetch fresh data in the background
    final hasConnection = await internetChecker.hasConnection;
    if (hasConnection) {
      try {
        final freshSpecifications =
            await remote.fetchCourseSpecifications(sessionId);
        await localSpecs.saveCourseSpecifications(freshSpecifications);
        yield freshSpecifications;
      } catch (_) {
        // If fresh fetch fails and we haven't yielded cached data yet, yield it now
        if (cachedSpecifications.isEmpty) {
          final fallbackSpecifications =
              await localSpecs.getCourseSpecifications(sessionId);
          yield fallbackSpecifications;
        }
        // If we already yielded cached data, we don't need to do anything
        // The UI will continue showing the cached data
      }
    } else if (cachedSpecifications.isEmpty) {
      // No connection and no cached data - yield empty list
      yield <CourseSpecification>[];
    }
  }

  Future<void> submitCourseRegistrations(
      List<Map<String, dynamic>> registrations) async {
    final hasConnection = await internetChecker.hasConnection;
    if (!hasConnection) {
      throw Exception('No internet connection');
    }

    await remote.submitCourseRegistrations(registrations);
  }
}

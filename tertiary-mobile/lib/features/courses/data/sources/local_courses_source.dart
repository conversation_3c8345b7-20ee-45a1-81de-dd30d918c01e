import 'package:drift/drift.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:tertiary_mobile/core/utils/local_database.dart';
import 'package:tertiary_mobile/core/cache/cache_manager.dart';
import 'package:tertiary_mobile/core/cache/cache_policy.dart';
import 'package:tertiary_mobile/core/utils/logger.dart';
import 'package:tertiary_mobile/features/courses/data/models/course_registration_model.dart';
import 'package:tertiary_mobile/features/courses/data/models/course_model.dart';
import 'package:tertiary_mobile/shared/data/models/department.dart';

part 'local_courses_source.g.dart';

@riverpod
LocalCourseRegistrationsSource localCourseRegistrationsSource(Ref ref) {
  final db = ref.read(localDatabaseProvider);
  final cacheManager = ref.read(cacheManagerProvider);
  return LocalCourseRegistrationsSource(db, cacheManager);
}

class LocalCourseRegistrationsSource {
  final LocalDatabase _db;
  final CacheManager _cacheManager;

  LocalCourseRegistrationsSource(this._db, this._cacheManager);

  Future<void> saveCourseRegistrations(List<CourseRegistration> regs) async {
    // Use session-specific cache key to avoid conflicts between sessions
    final sessionId = regs.isNotEmpty ? regs.first.session?.id : null;
    final cacheKey = sessionId != null
        ? 'course_registrations_session_$sessionId'
        : 'course_registrations';

    try {
      // Check if data has changed to avoid unnecessary operations
      final hasChanged = await _cacheManager.hasDataChanged(cacheKey, regs);
      if (!hasChanged) {
        logger
            .i('Course registrations data unchanged, skipping save operation');
        await _cacheManager.updateLastFetched(cacheKey);
        return;
      }

      await _db.batch((batch) {
        // Use upsert for courses
        final courses = regs.map((e) => e.course).toSet().toList();
        batch.insertAll(
          _db.coursesTable,
          courses.map(
            (course) => CoursesTableCompanion.insert(
              courseCode: course.courseCode,
              courseTitle: course.courseTitle,
              creditUnit: course.creditUnit,
              type: course.type ?? 'N/A',
              hostId: Value(course.host?.id ?? 0),
              departmentId: Value(course.department?.id ?? 0),
              courseSynopsis: course.courseSynopsis != null
                  ? Value(course.courseSynopsis!)
                  : const Value.absent(),
              passMark: course.passMark != null
                  ? Value(course.passMark!)
                  : const Value.absent(),
              lecturers: course.lecturers.isNotEmpty
                  ? Value(course.lecturers.join(','))
                  : const Value.absent(),
            ),
          ),
          mode: InsertMode.insertOrReplace,
        );
        // Use upsert for course registrations
        batch.insertAll(
          _db.courseRegistrationsTable,
          regs.map((r) => CourseRegistrationsTableCompanion.insert(
                id: Value(r.id),
                courseId: r.course.id,
                sessionId: Value(r.session?.id),
                semesterAccronym: Value(r.semesterAccronym),
                semesterPosition: Value(r.semesterPosition),
                semesterTitle: Value(r.semesterTitle),
                score: Value(r.score?.toString()),
                courseStatus: Value(r.courseStatus),
                approvalStatus: Value(r.approvalStatus),
                approvalRemark: Value(r.approvalRemark),
                remark: Value(r.remark),
                createdAt: Value(r.createdAt),
                updatedAt: Value(r.updatedAt),
              )),
          mode: InsertMode.insertOrReplace,
        );
      });

      // Update cache metadata
      await _cacheManager.updateCacheMetadata(
        cacheKey: cacheKey,
        recordCount: regs.length,
        policy: DataCachePolicies.courseRegistrations,
        dataHash: _cacheManager.generateDataHash(regs),
      );

      logger.i(
          'Successfully saved ${regs.length} course registrations using upsert operations');
    } catch (e) {
      logger.e('Error saving course registrations: $e');
      rethrow;
    }
  }

  /// Check if course registrations cache needs refresh based on cache policy
  Future<bool> needsRefresh([int? sessionId]) async {
    final cacheKey = sessionId != null
        ? 'course_registrations_session_$sessionId'
        : 'course_registrations';
    return await _cacheManager.needsRefresh(cacheKey);
  }

  /// Check if course registrations cache has expired
  Future<bool> isExpired([int? sessionId]) async {
    final cacheKey = sessionId != null
        ? 'course_registrations_session_$sessionId'
        : 'course_registrations';
    return await _cacheManager.isExpired(cacheKey);
  }

  Future<List<CourseRegistration>> getCourseRegistrations(
      [int? sessionId]) async {
    final cacheKey = sessionId != null
        ? 'course_registrations_session_$sessionId'
        : 'course_registrations';

    try {
      // Update last fetched time
      await _cacheManager.updateLastFetched(cacheKey);

      final query = _db.select(_db.courseRegistrationsTable);
      if (sessionId != null) {
        query.where((tbl) => tbl.sessionId.equals(sessionId));
      }
      final regRows = await query.get();
      final courseIds = regRows.map((r) => r.courseId).toSet();
      final courseRows = await (_db.select(_db.coursesTable)
            ..where((tbl) => tbl.id.isIn(courseIds.toList())))
          .get();

      // Map courseId to Course
      final courses = {
        for (final c in courseRows)
          c.id: Course(
            id: c.id,
            courseCode: c.courseCode,
            courseTitle: c.courseTitle,
            creditUnit: c.creditUnit,
            courseSynopsis: c.courseSynopsis,
            type: c.type,
            host: Department(
              id: c.hostId ?? 0,
              name: '',
            ),
            department: Department(
              id: c.departmentId ?? 0,
              name: '',
            ),
            passMark: c.passMark,
            lecturers: (c.lecturers ?? '')
                .split(',')
                .where((e) => e.isNotEmpty)
                .toList(),
          )
      };

      final registrations = regRows
          .map((row) => CourseRegistration(
                id: row.id,
                course: courses[row.courseId]!,
                session: null,
                semesterAccronym: row.semesterAccronym,
                semesterPosition: row.semesterPosition,
                semesterTitle: row.semesterTitle,
                score: row.score,
                courseStatus: row.courseStatus,
                approvalStatus: row.approvalStatus,
                approvalRemark: row.approvalRemark,
                remark: row.remark,
                createdAt: row.createdAt,
                updatedAt: row.updatedAt,
              ))
          .toList();

      logger.i(
          'Retrieved ${registrations.length} course registrations from cache');
      return registrations;
    } catch (e) {
      logger.e('Error retrieving course registrations: $e');
      rethrow;
    }
  }
}

// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'course_table_item.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CourseTableItem _$CourseTableItemFromJson(Map<String, dynamic> json) {
  return _CourseTableItem.fromJson(json);
}

/// @nodoc
mixin _$CourseTableItem {
  String get id => throw _privateConstructorUsedError;
  String get courseCode => throw _privateConstructorUsedError;
  String get courseTitle => throw _privateConstructorUsedError;
  int get creditUnit => throw _privateConstructorUsedError;
  bool get isRegistered =>
      throw _privateConstructorUsedError; // Source IDs for mapping back to original models
  String get sourceId => throw _privateConstructorUsedError;
  bool get isSpecification =>
      throw _privateConstructorUsedError; // Course information
  String? get courseType => throw _privateConstructorUsedError;
  String? get courseSynopsis =>
      throw _privateConstructorUsedError; // Registration specific
  String? get registrationStatus => throw _privateConstructorUsedError;
  String? get score => throw _privateConstructorUsedError;
  String? get approvalStatus => throw _privateConstructorUsedError;
  String? get approvalRemark => throw _privateConstructorUsedError;
  String? get semesterAccronym => throw _privateConstructorUsedError;
  String? get semesterTitle => throw _privateConstructorUsedError;
  int? get semesterPosition =>
      throw _privateConstructorUsedError; // Specification specific
  Session? get session => throw _privateConstructorUsedError;
  String? get courseStatus => throw _privateConstructorUsedError;
  bool? get isSiwes => throw _privateConstructorUsedError;
  bool? get hasPreRequisites => throw _privateConstructorUsedError;
  bool? get isUsedInResultComputation =>
      throw _privateConstructorUsedError; // Dates
  DateTime? get registrationDate => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this CourseTableItem to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CourseTableItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CourseTableItemCopyWith<CourseTableItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseTableItemCopyWith<$Res> {
  factory $CourseTableItemCopyWith(
          CourseTableItem value, $Res Function(CourseTableItem) then) =
      _$CourseTableItemCopyWithImpl<$Res, CourseTableItem>;
  @useResult
  $Res call(
      {String id,
      String courseCode,
      String courseTitle,
      int creditUnit,
      bool isRegistered,
      String sourceId,
      bool isSpecification,
      String? courseType,
      String? courseSynopsis,
      String? registrationStatus,
      String? score,
      String? approvalStatus,
      String? approvalRemark,
      String? semesterAccronym,
      String? semesterTitle,
      int? semesterPosition,
      Session? session,
      String? courseStatus,
      bool? isSiwes,
      bool? hasPreRequisites,
      bool? isUsedInResultComputation,
      DateTime? registrationDate,
      DateTime? updatedAt});

  $SessionCopyWith<$Res>? get session;
}

/// @nodoc
class _$CourseTableItemCopyWithImpl<$Res, $Val extends CourseTableItem>
    implements $CourseTableItemCopyWith<$Res> {
  _$CourseTableItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CourseTableItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? courseCode = null,
    Object? courseTitle = null,
    Object? creditUnit = null,
    Object? isRegistered = null,
    Object? sourceId = null,
    Object? isSpecification = null,
    Object? courseType = freezed,
    Object? courseSynopsis = freezed,
    Object? registrationStatus = freezed,
    Object? score = freezed,
    Object? approvalStatus = freezed,
    Object? approvalRemark = freezed,
    Object? semesterAccronym = freezed,
    Object? semesterTitle = freezed,
    Object? semesterPosition = freezed,
    Object? session = freezed,
    Object? courseStatus = freezed,
    Object? isSiwes = freezed,
    Object? hasPreRequisites = freezed,
    Object? isUsedInResultComputation = freezed,
    Object? registrationDate = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      courseCode: null == courseCode
          ? _value.courseCode
          : courseCode // ignore: cast_nullable_to_non_nullable
              as String,
      courseTitle: null == courseTitle
          ? _value.courseTitle
          : courseTitle // ignore: cast_nullable_to_non_nullable
              as String,
      creditUnit: null == creditUnit
          ? _value.creditUnit
          : creditUnit // ignore: cast_nullable_to_non_nullable
              as int,
      isRegistered: null == isRegistered
          ? _value.isRegistered
          : isRegistered // ignore: cast_nullable_to_non_nullable
              as bool,
      sourceId: null == sourceId
          ? _value.sourceId
          : sourceId // ignore: cast_nullable_to_non_nullable
              as String,
      isSpecification: null == isSpecification
          ? _value.isSpecification
          : isSpecification // ignore: cast_nullable_to_non_nullable
              as bool,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSynopsis: freezed == courseSynopsis
          ? _value.courseSynopsis
          : courseSynopsis // ignore: cast_nullable_to_non_nullable
              as String?,
      registrationStatus: freezed == registrationStatus
          ? _value.registrationStatus
          : registrationStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      score: freezed == score
          ? _value.score
          : score // ignore: cast_nullable_to_non_nullable
              as String?,
      approvalStatus: freezed == approvalStatus
          ? _value.approvalStatus
          : approvalStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      approvalRemark: freezed == approvalRemark
          ? _value.approvalRemark
          : approvalRemark // ignore: cast_nullable_to_non_nullable
              as String?,
      semesterAccronym: freezed == semesterAccronym
          ? _value.semesterAccronym
          : semesterAccronym // ignore: cast_nullable_to_non_nullable
              as String?,
      semesterTitle: freezed == semesterTitle
          ? _value.semesterTitle
          : semesterTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      semesterPosition: freezed == semesterPosition
          ? _value.semesterPosition
          : semesterPosition // ignore: cast_nullable_to_non_nullable
              as int?,
      session: freezed == session
          ? _value.session
          : session // ignore: cast_nullable_to_non_nullable
              as Session?,
      courseStatus: freezed == courseStatus
          ? _value.courseStatus
          : courseStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      isSiwes: freezed == isSiwes
          ? _value.isSiwes
          : isSiwes // ignore: cast_nullable_to_non_nullable
              as bool?,
      hasPreRequisites: freezed == hasPreRequisites
          ? _value.hasPreRequisites
          : hasPreRequisites // ignore: cast_nullable_to_non_nullable
              as bool?,
      isUsedInResultComputation: freezed == isUsedInResultComputation
          ? _value.isUsedInResultComputation
          : isUsedInResultComputation // ignore: cast_nullable_to_non_nullable
              as bool?,
      registrationDate: freezed == registrationDate
          ? _value.registrationDate
          : registrationDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }

  /// Create a copy of CourseTableItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SessionCopyWith<$Res>? get session {
    if (_value.session == null) {
      return null;
    }

    return $SessionCopyWith<$Res>(_value.session!, (value) {
      return _then(_value.copyWith(session: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CourseTableItemImplCopyWith<$Res>
    implements $CourseTableItemCopyWith<$Res> {
  factory _$$CourseTableItemImplCopyWith(_$CourseTableItemImpl value,
          $Res Function(_$CourseTableItemImpl) then) =
      __$$CourseTableItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String courseCode,
      String courseTitle,
      int creditUnit,
      bool isRegistered,
      String sourceId,
      bool isSpecification,
      String? courseType,
      String? courseSynopsis,
      String? registrationStatus,
      String? score,
      String? approvalStatus,
      String? approvalRemark,
      String? semesterAccronym,
      String? semesterTitle,
      int? semesterPosition,
      Session? session,
      String? courseStatus,
      bool? isSiwes,
      bool? hasPreRequisites,
      bool? isUsedInResultComputation,
      DateTime? registrationDate,
      DateTime? updatedAt});

  @override
  $SessionCopyWith<$Res>? get session;
}

/// @nodoc
class __$$CourseTableItemImplCopyWithImpl<$Res>
    extends _$CourseTableItemCopyWithImpl<$Res, _$CourseTableItemImpl>
    implements _$$CourseTableItemImplCopyWith<$Res> {
  __$$CourseTableItemImplCopyWithImpl(
      _$CourseTableItemImpl _value, $Res Function(_$CourseTableItemImpl) _then)
      : super(_value, _then);

  /// Create a copy of CourseTableItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? courseCode = null,
    Object? courseTitle = null,
    Object? creditUnit = null,
    Object? isRegistered = null,
    Object? sourceId = null,
    Object? isSpecification = null,
    Object? courseType = freezed,
    Object? courseSynopsis = freezed,
    Object? registrationStatus = freezed,
    Object? score = freezed,
    Object? approvalStatus = freezed,
    Object? approvalRemark = freezed,
    Object? semesterAccronym = freezed,
    Object? semesterTitle = freezed,
    Object? semesterPosition = freezed,
    Object? session = freezed,
    Object? courseStatus = freezed,
    Object? isSiwes = freezed,
    Object? hasPreRequisites = freezed,
    Object? isUsedInResultComputation = freezed,
    Object? registrationDate = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$CourseTableItemImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      courseCode: null == courseCode
          ? _value.courseCode
          : courseCode // ignore: cast_nullable_to_non_nullable
              as String,
      courseTitle: null == courseTitle
          ? _value.courseTitle
          : courseTitle // ignore: cast_nullable_to_non_nullable
              as String,
      creditUnit: null == creditUnit
          ? _value.creditUnit
          : creditUnit // ignore: cast_nullable_to_non_nullable
              as int,
      isRegistered: null == isRegistered
          ? _value.isRegistered
          : isRegistered // ignore: cast_nullable_to_non_nullable
              as bool,
      sourceId: null == sourceId
          ? _value.sourceId
          : sourceId // ignore: cast_nullable_to_non_nullable
              as String,
      isSpecification: null == isSpecification
          ? _value.isSpecification
          : isSpecification // ignore: cast_nullable_to_non_nullable
              as bool,
      courseType: freezed == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as String?,
      courseSynopsis: freezed == courseSynopsis
          ? _value.courseSynopsis
          : courseSynopsis // ignore: cast_nullable_to_non_nullable
              as String?,
      registrationStatus: freezed == registrationStatus
          ? _value.registrationStatus
          : registrationStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      score: freezed == score
          ? _value.score
          : score // ignore: cast_nullable_to_non_nullable
              as String?,
      approvalStatus: freezed == approvalStatus
          ? _value.approvalStatus
          : approvalStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      approvalRemark: freezed == approvalRemark
          ? _value.approvalRemark
          : approvalRemark // ignore: cast_nullable_to_non_nullable
              as String?,
      semesterAccronym: freezed == semesterAccronym
          ? _value.semesterAccronym
          : semesterAccronym // ignore: cast_nullable_to_non_nullable
              as String?,
      semesterTitle: freezed == semesterTitle
          ? _value.semesterTitle
          : semesterTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      semesterPosition: freezed == semesterPosition
          ? _value.semesterPosition
          : semesterPosition // ignore: cast_nullable_to_non_nullable
              as int?,
      session: freezed == session
          ? _value.session
          : session // ignore: cast_nullable_to_non_nullable
              as Session?,
      courseStatus: freezed == courseStatus
          ? _value.courseStatus
          : courseStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      isSiwes: freezed == isSiwes
          ? _value.isSiwes
          : isSiwes // ignore: cast_nullable_to_non_nullable
              as bool?,
      hasPreRequisites: freezed == hasPreRequisites
          ? _value.hasPreRequisites
          : hasPreRequisites // ignore: cast_nullable_to_non_nullable
              as bool?,
      isUsedInResultComputation: freezed == isUsedInResultComputation
          ? _value.isUsedInResultComputation
          : isUsedInResultComputation // ignore: cast_nullable_to_non_nullable
              as bool?,
      registrationDate: freezed == registrationDate
          ? _value.registrationDate
          : registrationDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CourseTableItemImpl extends _CourseTableItem {
  const _$CourseTableItemImpl(
      {required this.id,
      required this.courseCode,
      required this.courseTitle,
      required this.creditUnit,
      required this.isRegistered,
      required this.sourceId,
      required this.isSpecification,
      this.courseType,
      this.courseSynopsis,
      this.registrationStatus,
      this.score,
      this.approvalStatus,
      this.approvalRemark,
      this.semesterAccronym,
      this.semesterTitle,
      this.semesterPosition,
      this.session,
      this.courseStatus,
      this.isSiwes,
      this.hasPreRequisites,
      this.isUsedInResultComputation,
      this.registrationDate,
      this.updatedAt})
      : super._();

  factory _$CourseTableItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$CourseTableItemImplFromJson(json);

  @override
  final String id;
  @override
  final String courseCode;
  @override
  final String courseTitle;
  @override
  final int creditUnit;
  @override
  final bool isRegistered;
// Source IDs for mapping back to original models
  @override
  final String sourceId;
  @override
  final bool isSpecification;
// Course information
  @override
  final String? courseType;
  @override
  final String? courseSynopsis;
// Registration specific
  @override
  final String? registrationStatus;
  @override
  final String? score;
  @override
  final String? approvalStatus;
  @override
  final String? approvalRemark;
  @override
  final String? semesterAccronym;
  @override
  final String? semesterTitle;
  @override
  final int? semesterPosition;
// Specification specific
  @override
  final Session? session;
  @override
  final String? courseStatus;
  @override
  final bool? isSiwes;
  @override
  final bool? hasPreRequisites;
  @override
  final bool? isUsedInResultComputation;
// Dates
  @override
  final DateTime? registrationDate;
  @override
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'CourseTableItem(id: $id, courseCode: $courseCode, courseTitle: $courseTitle, creditUnit: $creditUnit, isRegistered: $isRegistered, sourceId: $sourceId, isSpecification: $isSpecification, courseType: $courseType, courseSynopsis: $courseSynopsis, registrationStatus: $registrationStatus, score: $score, approvalStatus: $approvalStatus, approvalRemark: $approvalRemark, semesterAccronym: $semesterAccronym, semesterTitle: $semesterTitle, semesterPosition: $semesterPosition, session: $session, courseStatus: $courseStatus, isSiwes: $isSiwes, hasPreRequisites: $hasPreRequisites, isUsedInResultComputation: $isUsedInResultComputation, registrationDate: $registrationDate, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CourseTableItemImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.courseCode, courseCode) ||
                other.courseCode == courseCode) &&
            (identical(other.courseTitle, courseTitle) ||
                other.courseTitle == courseTitle) &&
            (identical(other.creditUnit, creditUnit) ||
                other.creditUnit == creditUnit) &&
            (identical(other.isRegistered, isRegistered) ||
                other.isRegistered == isRegistered) &&
            (identical(other.sourceId, sourceId) ||
                other.sourceId == sourceId) &&
            (identical(other.isSpecification, isSpecification) ||
                other.isSpecification == isSpecification) &&
            (identical(other.courseType, courseType) ||
                other.courseType == courseType) &&
            (identical(other.courseSynopsis, courseSynopsis) ||
                other.courseSynopsis == courseSynopsis) &&
            (identical(other.registrationStatus, registrationStatus) ||
                other.registrationStatus == registrationStatus) &&
            (identical(other.score, score) || other.score == score) &&
            (identical(other.approvalStatus, approvalStatus) ||
                other.approvalStatus == approvalStatus) &&
            (identical(other.approvalRemark, approvalRemark) ||
                other.approvalRemark == approvalRemark) &&
            (identical(other.semesterAccronym, semesterAccronym) ||
                other.semesterAccronym == semesterAccronym) &&
            (identical(other.semesterTitle, semesterTitle) ||
                other.semesterTitle == semesterTitle) &&
            (identical(other.semesterPosition, semesterPosition) ||
                other.semesterPosition == semesterPosition) &&
            (identical(other.session, session) || other.session == session) &&
            (identical(other.courseStatus, courseStatus) ||
                other.courseStatus == courseStatus) &&
            (identical(other.isSiwes, isSiwes) || other.isSiwes == isSiwes) &&
            (identical(other.hasPreRequisites, hasPreRequisites) ||
                other.hasPreRequisites == hasPreRequisites) &&
            (identical(other.isUsedInResultComputation,
                    isUsedInResultComputation) ||
                other.isUsedInResultComputation == isUsedInResultComputation) &&
            (identical(other.registrationDate, registrationDate) ||
                other.registrationDate == registrationDate) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        courseCode,
        courseTitle,
        creditUnit,
        isRegistered,
        sourceId,
        isSpecification,
        courseType,
        courseSynopsis,
        registrationStatus,
        score,
        approvalStatus,
        approvalRemark,
        semesterAccronym,
        semesterTitle,
        semesterPosition,
        session,
        courseStatus,
        isSiwes,
        hasPreRequisites,
        isUsedInResultComputation,
        registrationDate,
        updatedAt
      ]);

  /// Create a copy of CourseTableItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CourseTableItemImplCopyWith<_$CourseTableItemImpl> get copyWith =>
      __$$CourseTableItemImplCopyWithImpl<_$CourseTableItemImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CourseTableItemImplToJson(
      this,
    );
  }
}

abstract class _CourseTableItem extends CourseTableItem {
  const factory _CourseTableItem(
      {required final String id,
      required final String courseCode,
      required final String courseTitle,
      required final int creditUnit,
      required final bool isRegistered,
      required final String sourceId,
      required final bool isSpecification,
      final String? courseType,
      final String? courseSynopsis,
      final String? registrationStatus,
      final String? score,
      final String? approvalStatus,
      final String? approvalRemark,
      final String? semesterAccronym,
      final String? semesterTitle,
      final int? semesterPosition,
      final Session? session,
      final String? courseStatus,
      final bool? isSiwes,
      final bool? hasPreRequisites,
      final bool? isUsedInResultComputation,
      final DateTime? registrationDate,
      final DateTime? updatedAt}) = _$CourseTableItemImpl;
  const _CourseTableItem._() : super._();

  factory _CourseTableItem.fromJson(Map<String, dynamic> json) =
      _$CourseTableItemImpl.fromJson;

  @override
  String get id;
  @override
  String get courseCode;
  @override
  String get courseTitle;
  @override
  int get creditUnit;
  @override
  bool get isRegistered; // Source IDs for mapping back to original models
  @override
  String get sourceId;
  @override
  bool get isSpecification; // Course information
  @override
  String? get courseType;
  @override
  String? get courseSynopsis; // Registration specific
  @override
  String? get registrationStatus;
  @override
  String? get score;
  @override
  String? get approvalStatus;
  @override
  String? get approvalRemark;
  @override
  String? get semesterAccronym;
  @override
  String? get semesterTitle;
  @override
  int? get semesterPosition; // Specification specific
  @override
  Session? get session;
  @override
  String? get courseStatus;
  @override
  bool? get isSiwes;
  @override
  bool? get hasPreRequisites;
  @override
  bool? get isUsedInResultComputation; // Dates
  @override
  DateTime? get registrationDate;
  @override
  DateTime? get updatedAt;

  /// Create a copy of CourseTableItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CourseTableItemImplCopyWith<_$CourseTableItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

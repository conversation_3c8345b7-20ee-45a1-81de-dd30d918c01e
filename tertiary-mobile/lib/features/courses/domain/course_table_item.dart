import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:tertiary_mobile/features/courses/data/models/course_registration_model.dart';
import 'package:tertiary_mobile/features/courses/data/models/course_specification_model.dart';
import 'package:tertiary_mobile/shared/data/models/session_semester_model.dart';

part 'course_table_item.freezed.dart';
part 'course_table_item.g.dart';

@freezed
class CourseTableItem with _$CourseTableItem {
  const factory CourseTableItem({
    required String id,
    required String courseCode,
    required String courseTitle,
    required int creditUnit,
    required bool isRegistered,

    // Source IDs for mapping back to original models
    required String sourceId,
    required bool isSpecification,

    // Course information
    String? courseType,
    String? courseSynopsis,

    // Registration specific
    String? registrationStatus,
    String? score,
    String? approvalStatus,
    String? approvalRemark,
    String? semesterAccronym,
    String? semesterTitle,
    int? semesterPosition,

    // Specification specific
    Session? session,
    String? courseStatus,
    bool? isSiwes,
    bool? hasPreRequisites,
    bool? isUsedInResultComputation,

    // Dates
    DateTime? registrationDate,
    DateTime? updatedAt,
  }) = _CourseTableItem;

  const CourseTableItem._();

  factory CourseTableItem.fromJson(Map<String, dynamic> json) =>
      _$CourseTableItemFromJson(json);

  /// Format course type for display: first letter, or 2 letters if multi-word
  String get formattedCourseType {
    if (courseType == null || courseType!.isEmpty) return 'N/A';

    final type = courseType!.toLowerCase();
    final words = type.split(' ').where((word) => word.isNotEmpty).toList();

    if (words.isEmpty) return 'N/A';

    if (words.length == 1) {
      // Single word: return first letter capitalized
      return words.first.substring(0, 1).toUpperCase();
    } else {
      // Multiple words: return first letter of first two words capitalized
      final first = words.first.substring(0, 1).toUpperCase();
      final second =
          words.length > 1 ? words[1].substring(0, 1).toUpperCase() : '';
      return '$first$second';
    }
  }
}

// Extensions to convert from domain models to DTO
extension CourseRegistrationToTableItem on CourseRegistration {
  CourseTableItem toTableItem() {
    return CourseTableItem(
      id: 'reg_$id',
      courseCode: course.courseCode,
      courseTitle: course.courseTitle,
      creditUnit: course.creditUnit,
      isRegistered: true,
      sourceId: id.toString(),
      isSpecification: false,
      courseType: course.type,
      courseSynopsis: course.courseSynopsis,
      registrationStatus: courseStatus,
      score: score,
      approvalStatus: approvalStatus,
      approvalRemark: approvalRemark,
      semesterAccronym: semesterAccronym,
      semesterTitle: semesterTitle,
      semesterPosition: semesterPosition,
      registrationDate:
          createdAt != null ? DateTime.tryParse(createdAt!) : null,
      updatedAt: updatedAt != null ? DateTime.tryParse(updatedAt!) : null,
    );
  }
}

extension CourseSpecificationToTableItem on CourseSpecification {
  CourseTableItem toTableItem() {
    return CourseTableItem(
      id: 'spec_$id',
      courseCode: course.courseCode,
      courseTitle: course.courseTitle,
      creditUnit: creditUnit,
      isRegistered: false,
      sourceId: id.toString(),
      isSpecification: true,
      courseType: course.type,
      courseSynopsis: course.courseSynopsis,
      courseStatus: courseStatus,
      isSiwes: isSiwes == 1,
      hasPreRequisites: hasPreRequisites == 1,
      isUsedInResultComputation: isUsedInResultComputation == 1,
      semesterAccronym: semesterAccronym,
      semesterTitle: semesterTitle,
      session: session,
      registrationDate: null,
      updatedAt: null,
    );
  }
}

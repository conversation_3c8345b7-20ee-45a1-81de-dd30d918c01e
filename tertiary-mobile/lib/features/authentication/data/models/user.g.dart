// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserImpl _$$UserImplFromJson(Map<String, dynamic> json) => _$UserImpl(
      id: (json['id'] as num).toInt(),
      userId: (json['user_id'] as num?)?.toInt(),
      fullname: json['fullname'] as String?,
      profilePictureUrl: json['profile_picture_url'] as String?,
      studentNumber: json['student_number'] as String?,
      jambRegistrationNumber: json['jamb_registration_number'] as String?,
      email: json['email'] as String?,
      phoneNumber: json['phone_number'] as String?,
      gender: json['gender'] as String?,
      nationality: json['nationality'] as String?,
      stateOfOrigin: json['state_of_origin'] as String?,
      lga: json['lga'] as String?,
      dateOfBirth: json['date_of_birth'] as String?,
      bloodGroup: json['blood_group'] as String?,
      genotype: json['genotype'] as String?,
      religion: json['religion'] as String?,
      maritalStatus: json['marital_status'] as String?,
      faculty: json['faculty'] == null
          ? null
          : Faculty.fromJson(json['faculty'] as Map<String, dynamic>),
      department: json['department'] == null
          ? null
          : Department.fromJson(json['department'] as Map<String, dynamic>),
      programme: json['programme'] == null
          ? null
          : Programme.fromJson(json['programme'] as Map<String, dynamic>),
      academicLevel: json['academic_level'] == null
          ? null
          : AcademicLevel.fromJson(
              json['academic_level'] as Map<String, dynamic>),
      admissionYear: json['admission_year'] as String?,
      graduationYear: json['graduation_year'] as String?,
      programDuration: json['program_duration'] as String?,
      admissionType: json['admission_type'] as String?,
      role: json['role'] as String?,
      currentAcademicSessionWithSemesters:
          json['current_academic_session_with_semesters'] == null
              ? null
              : Session.fromJson(json['current_academic_session_with_semesters']
                  as Map<String, dynamic>),
    );

Map<String, dynamic> _$$UserImplToJson(_$UserImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user_id': instance.userId,
      'fullname': instance.fullname,
      'profile_picture_url': instance.profilePictureUrl,
      'student_number': instance.studentNumber,
      'jamb_registration_number': instance.jambRegistrationNumber,
      'email': instance.email,
      'phone_number': instance.phoneNumber,
      'gender': instance.gender,
      'nationality': instance.nationality,
      'state_of_origin': instance.stateOfOrigin,
      'lga': instance.lga,
      'date_of_birth': instance.dateOfBirth,
      'blood_group': instance.bloodGroup,
      'genotype': instance.genotype,
      'religion': instance.religion,
      'marital_status': instance.maritalStatus,
      'faculty': instance.faculty?.toJson(),
      'department': instance.department?.toJson(),
      'programme': instance.programme?.toJson(),
      'academic_level': instance.academicLevel?.toJson(),
      'admission_year': instance.admissionYear,
      'graduation_year': instance.graduationYear,
      'program_duration': instance.programDuration,
      'admission_type': instance.admissionType,
      'role': instance.role,
      'current_academic_session_with_semesters':
          instance.currentAcademicSessionWithSemesters?.toJson(),
    };

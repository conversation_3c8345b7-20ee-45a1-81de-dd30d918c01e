// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:tertiary_mobile/shared/data/models/faculty.dart';
import 'package:tertiary_mobile/shared/data/models/department.dart';
import 'package:tertiary_mobile/shared/data/models/programme.dart';
import 'package:tertiary_mobile/shared/data/models/academic_level.dart';
import 'package:tertiary_mobile/shared/data/models/session_semester_model.dart';

part 'user.freezed.dart';
part 'user.g.dart';

@freezed
class User with _$User {
  const factory User({
    required int id,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'user_id') int? userId,
    String? fullname,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'profile_picture_url') String? profilePictureUrl,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'student_number') String? studentNumber,
    @Json<PERSON>ey(name: 'jamb_registration_number') String? jambRegistrationNumber,
    String? email,
    @Json<PERSON>ey(name: 'phone_number') String? phoneNumber,
    String? gender,
    String? nationality,
    @J<PERSON><PERSON><PERSON>(name: 'state_of_origin') String? stateOfOrigin,
    String? lga,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'date_of_birth') String? dateOfBirth,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'blood_group') String? bloodGroup,
    String? genotype,
    String? religion,
    @JsonKey(name: 'marital_status') String? maritalStatus,
    Faculty? faculty,
    Department? department,
    Programme? programme,
    @JsonKey(name: 'academic_level') AcademicLevel? academicLevel,
    @JsonKey(name: 'admission_year') String? admissionYear,
    @JsonKey(name: 'graduation_year') String? graduationYear,
    @JsonKey(name: 'program_duration') String? programDuration,
    @JsonKey(name: 'admission_type') String? admissionType,
    String? role,
    @JsonKey(name: 'current_academic_session_with_semesters')
    Session? currentAcademicSessionWithSemesters,
  }) = _User;

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
}

// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

User _$UserFromJson(Map<String, dynamic> json) {
  return _User.fromJson(json);
}

/// @nodoc
mixin _$User {
  int get id => throw _privateConstructorUsedError;
  @JsonKey(name: 'user_id')
  int? get userId => throw _privateConstructorUsedError;
  String? get fullname => throw _privateConstructorUsedError;
  @JsonKey(name: 'profile_picture_url')
  String? get profilePictureUrl => throw _privateConstructorUsedError;
  @JsonKey(name: 'student_number')
  String? get studentNumber => throw _privateConstructorUsedError;
  @JsonKey(name: 'jamb_registration_number')
  String? get jambRegistrationNumber => throw _privateConstructorUsedError;
  String? get email => throw _privateConstructorUsedError;
  @JsonKey(name: 'phone_number')
  String? get phoneNumber => throw _privateConstructorUsedError;
  String? get gender => throw _privateConstructorUsedError;
  String? get nationality => throw _privateConstructorUsedError;
  @JsonKey(name: 'state_of_origin')
  String? get stateOfOrigin => throw _privateConstructorUsedError;
  String? get lga => throw _privateConstructorUsedError;
  @JsonKey(name: 'date_of_birth')
  String? get dateOfBirth => throw _privateConstructorUsedError;
  @JsonKey(name: 'blood_group')
  String? get bloodGroup => throw _privateConstructorUsedError;
  String? get genotype => throw _privateConstructorUsedError;
  String? get religion => throw _privateConstructorUsedError;
  @JsonKey(name: 'marital_status')
  String? get maritalStatus => throw _privateConstructorUsedError;
  Faculty? get faculty => throw _privateConstructorUsedError;
  Department? get department => throw _privateConstructorUsedError;
  Programme? get programme => throw _privateConstructorUsedError;
  @JsonKey(name: 'academic_level')
  AcademicLevel? get academicLevel => throw _privateConstructorUsedError;
  @JsonKey(name: 'admission_year')
  String? get admissionYear => throw _privateConstructorUsedError;
  @JsonKey(name: 'graduation_year')
  String? get graduationYear => throw _privateConstructorUsedError;
  @JsonKey(name: 'program_duration')
  String? get programDuration => throw _privateConstructorUsedError;
  @JsonKey(name: 'admission_type')
  String? get admissionType => throw _privateConstructorUsedError;
  String? get role => throw _privateConstructorUsedError;
  @JsonKey(name: 'current_academic_session_with_semesters')
  Session? get currentAcademicSessionWithSemesters =>
      throw _privateConstructorUsedError;

  /// Serializes this User to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserCopyWith<User> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserCopyWith<$Res> {
  factory $UserCopyWith(User value, $Res Function(User) then) =
      _$UserCopyWithImpl<$Res, User>;
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'user_id') int? userId,
      String? fullname,
      @JsonKey(name: 'profile_picture_url') String? profilePictureUrl,
      @JsonKey(name: 'student_number') String? studentNumber,
      @JsonKey(name: 'jamb_registration_number') String? jambRegistrationNumber,
      String? email,
      @JsonKey(name: 'phone_number') String? phoneNumber,
      String? gender,
      String? nationality,
      @JsonKey(name: 'state_of_origin') String? stateOfOrigin,
      String? lga,
      @JsonKey(name: 'date_of_birth') String? dateOfBirth,
      @JsonKey(name: 'blood_group') String? bloodGroup,
      String? genotype,
      String? religion,
      @JsonKey(name: 'marital_status') String? maritalStatus,
      Faculty? faculty,
      Department? department,
      Programme? programme,
      @JsonKey(name: 'academic_level') AcademicLevel? academicLevel,
      @JsonKey(name: 'admission_year') String? admissionYear,
      @JsonKey(name: 'graduation_year') String? graduationYear,
      @JsonKey(name: 'program_duration') String? programDuration,
      @JsonKey(name: 'admission_type') String? admissionType,
      String? role,
      @JsonKey(name: 'current_academic_session_with_semesters')
      Session? currentAcademicSessionWithSemesters});

  $FacultyCopyWith<$Res>? get faculty;
  $DepartmentCopyWith<$Res>? get department;
  $ProgrammeCopyWith<$Res>? get programme;
  $AcademicLevelCopyWith<$Res>? get academicLevel;
  $SessionCopyWith<$Res>? get currentAcademicSessionWithSemesters;
}

/// @nodoc
class _$UserCopyWithImpl<$Res, $Val extends User>
    implements $UserCopyWith<$Res> {
  _$UserCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = freezed,
    Object? fullname = freezed,
    Object? profilePictureUrl = freezed,
    Object? studentNumber = freezed,
    Object? jambRegistrationNumber = freezed,
    Object? email = freezed,
    Object? phoneNumber = freezed,
    Object? gender = freezed,
    Object? nationality = freezed,
    Object? stateOfOrigin = freezed,
    Object? lga = freezed,
    Object? dateOfBirth = freezed,
    Object? bloodGroup = freezed,
    Object? genotype = freezed,
    Object? religion = freezed,
    Object? maritalStatus = freezed,
    Object? faculty = freezed,
    Object? department = freezed,
    Object? programme = freezed,
    Object? academicLevel = freezed,
    Object? admissionYear = freezed,
    Object? graduationYear = freezed,
    Object? programDuration = freezed,
    Object? admissionType = freezed,
    Object? role = freezed,
    Object? currentAcademicSessionWithSemesters = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
      fullname: freezed == fullname
          ? _value.fullname
          : fullname // ignore: cast_nullable_to_non_nullable
              as String?,
      profilePictureUrl: freezed == profilePictureUrl
          ? _value.profilePictureUrl
          : profilePictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      studentNumber: freezed == studentNumber
          ? _value.studentNumber
          : studentNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      jambRegistrationNumber: freezed == jambRegistrationNumber
          ? _value.jambRegistrationNumber
          : jambRegistrationNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      gender: freezed == gender
          ? _value.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as String?,
      nationality: freezed == nationality
          ? _value.nationality
          : nationality // ignore: cast_nullable_to_non_nullable
              as String?,
      stateOfOrigin: freezed == stateOfOrigin
          ? _value.stateOfOrigin
          : stateOfOrigin // ignore: cast_nullable_to_non_nullable
              as String?,
      lga: freezed == lga
          ? _value.lga
          : lga // ignore: cast_nullable_to_non_nullable
              as String?,
      dateOfBirth: freezed == dateOfBirth
          ? _value.dateOfBirth
          : dateOfBirth // ignore: cast_nullable_to_non_nullable
              as String?,
      bloodGroup: freezed == bloodGroup
          ? _value.bloodGroup
          : bloodGroup // ignore: cast_nullable_to_non_nullable
              as String?,
      genotype: freezed == genotype
          ? _value.genotype
          : genotype // ignore: cast_nullable_to_non_nullable
              as String?,
      religion: freezed == religion
          ? _value.religion
          : religion // ignore: cast_nullable_to_non_nullable
              as String?,
      maritalStatus: freezed == maritalStatus
          ? _value.maritalStatus
          : maritalStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      faculty: freezed == faculty
          ? _value.faculty
          : faculty // ignore: cast_nullable_to_non_nullable
              as Faculty?,
      department: freezed == department
          ? _value.department
          : department // ignore: cast_nullable_to_non_nullable
              as Department?,
      programme: freezed == programme
          ? _value.programme
          : programme // ignore: cast_nullable_to_non_nullable
              as Programme?,
      academicLevel: freezed == academicLevel
          ? _value.academicLevel
          : academicLevel // ignore: cast_nullable_to_non_nullable
              as AcademicLevel?,
      admissionYear: freezed == admissionYear
          ? _value.admissionYear
          : admissionYear // ignore: cast_nullable_to_non_nullable
              as String?,
      graduationYear: freezed == graduationYear
          ? _value.graduationYear
          : graduationYear // ignore: cast_nullable_to_non_nullable
              as String?,
      programDuration: freezed == programDuration
          ? _value.programDuration
          : programDuration // ignore: cast_nullable_to_non_nullable
              as String?,
      admissionType: freezed == admissionType
          ? _value.admissionType
          : admissionType // ignore: cast_nullable_to_non_nullable
              as String?,
      role: freezed == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as String?,
      currentAcademicSessionWithSemesters: freezed ==
              currentAcademicSessionWithSemesters
          ? _value.currentAcademicSessionWithSemesters
          : currentAcademicSessionWithSemesters // ignore: cast_nullable_to_non_nullable
              as Session?,
    ) as $Val);
  }

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $FacultyCopyWith<$Res>? get faculty {
    if (_value.faculty == null) {
      return null;
    }

    return $FacultyCopyWith<$Res>(_value.faculty!, (value) {
      return _then(_value.copyWith(faculty: value) as $Val);
    });
  }

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $DepartmentCopyWith<$Res>? get department {
    if (_value.department == null) {
      return null;
    }

    return $DepartmentCopyWith<$Res>(_value.department!, (value) {
      return _then(_value.copyWith(department: value) as $Val);
    });
  }

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProgrammeCopyWith<$Res>? get programme {
    if (_value.programme == null) {
      return null;
    }

    return $ProgrammeCopyWith<$Res>(_value.programme!, (value) {
      return _then(_value.copyWith(programme: value) as $Val);
    });
  }

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AcademicLevelCopyWith<$Res>? get academicLevel {
    if (_value.academicLevel == null) {
      return null;
    }

    return $AcademicLevelCopyWith<$Res>(_value.academicLevel!, (value) {
      return _then(_value.copyWith(academicLevel: value) as $Val);
    });
  }

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SessionCopyWith<$Res>? get currentAcademicSessionWithSemesters {
    if (_value.currentAcademicSessionWithSemesters == null) {
      return null;
    }

    return $SessionCopyWith<$Res>(_value.currentAcademicSessionWithSemesters!,
        (value) {
      return _then(
          _value.copyWith(currentAcademicSessionWithSemesters: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$UserImplCopyWith<$Res> implements $UserCopyWith<$Res> {
  factory _$$UserImplCopyWith(
          _$UserImpl value, $Res Function(_$UserImpl) then) =
      __$$UserImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'user_id') int? userId,
      String? fullname,
      @JsonKey(name: 'profile_picture_url') String? profilePictureUrl,
      @JsonKey(name: 'student_number') String? studentNumber,
      @JsonKey(name: 'jamb_registration_number') String? jambRegistrationNumber,
      String? email,
      @JsonKey(name: 'phone_number') String? phoneNumber,
      String? gender,
      String? nationality,
      @JsonKey(name: 'state_of_origin') String? stateOfOrigin,
      String? lga,
      @JsonKey(name: 'date_of_birth') String? dateOfBirth,
      @JsonKey(name: 'blood_group') String? bloodGroup,
      String? genotype,
      String? religion,
      @JsonKey(name: 'marital_status') String? maritalStatus,
      Faculty? faculty,
      Department? department,
      Programme? programme,
      @JsonKey(name: 'academic_level') AcademicLevel? academicLevel,
      @JsonKey(name: 'admission_year') String? admissionYear,
      @JsonKey(name: 'graduation_year') String? graduationYear,
      @JsonKey(name: 'program_duration') String? programDuration,
      @JsonKey(name: 'admission_type') String? admissionType,
      String? role,
      @JsonKey(name: 'current_academic_session_with_semesters')
      Session? currentAcademicSessionWithSemesters});

  @override
  $FacultyCopyWith<$Res>? get faculty;
  @override
  $DepartmentCopyWith<$Res>? get department;
  @override
  $ProgrammeCopyWith<$Res>? get programme;
  @override
  $AcademicLevelCopyWith<$Res>? get academicLevel;
  @override
  $SessionCopyWith<$Res>? get currentAcademicSessionWithSemesters;
}

/// @nodoc
class __$$UserImplCopyWithImpl<$Res>
    extends _$UserCopyWithImpl<$Res, _$UserImpl>
    implements _$$UserImplCopyWith<$Res> {
  __$$UserImplCopyWithImpl(_$UserImpl _value, $Res Function(_$UserImpl) _then)
      : super(_value, _then);

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = freezed,
    Object? fullname = freezed,
    Object? profilePictureUrl = freezed,
    Object? studentNumber = freezed,
    Object? jambRegistrationNumber = freezed,
    Object? email = freezed,
    Object? phoneNumber = freezed,
    Object? gender = freezed,
    Object? nationality = freezed,
    Object? stateOfOrigin = freezed,
    Object? lga = freezed,
    Object? dateOfBirth = freezed,
    Object? bloodGroup = freezed,
    Object? genotype = freezed,
    Object? religion = freezed,
    Object? maritalStatus = freezed,
    Object? faculty = freezed,
    Object? department = freezed,
    Object? programme = freezed,
    Object? academicLevel = freezed,
    Object? admissionYear = freezed,
    Object? graduationYear = freezed,
    Object? programDuration = freezed,
    Object? admissionType = freezed,
    Object? role = freezed,
    Object? currentAcademicSessionWithSemesters = freezed,
  }) {
    return _then(_$UserImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
      fullname: freezed == fullname
          ? _value.fullname
          : fullname // ignore: cast_nullable_to_non_nullable
              as String?,
      profilePictureUrl: freezed == profilePictureUrl
          ? _value.profilePictureUrl
          : profilePictureUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      studentNumber: freezed == studentNumber
          ? _value.studentNumber
          : studentNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      jambRegistrationNumber: freezed == jambRegistrationNumber
          ? _value.jambRegistrationNumber
          : jambRegistrationNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      gender: freezed == gender
          ? _value.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as String?,
      nationality: freezed == nationality
          ? _value.nationality
          : nationality // ignore: cast_nullable_to_non_nullable
              as String?,
      stateOfOrigin: freezed == stateOfOrigin
          ? _value.stateOfOrigin
          : stateOfOrigin // ignore: cast_nullable_to_non_nullable
              as String?,
      lga: freezed == lga
          ? _value.lga
          : lga // ignore: cast_nullable_to_non_nullable
              as String?,
      dateOfBirth: freezed == dateOfBirth
          ? _value.dateOfBirth
          : dateOfBirth // ignore: cast_nullable_to_non_nullable
              as String?,
      bloodGroup: freezed == bloodGroup
          ? _value.bloodGroup
          : bloodGroup // ignore: cast_nullable_to_non_nullable
              as String?,
      genotype: freezed == genotype
          ? _value.genotype
          : genotype // ignore: cast_nullable_to_non_nullable
              as String?,
      religion: freezed == religion
          ? _value.religion
          : religion // ignore: cast_nullable_to_non_nullable
              as String?,
      maritalStatus: freezed == maritalStatus
          ? _value.maritalStatus
          : maritalStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      faculty: freezed == faculty
          ? _value.faculty
          : faculty // ignore: cast_nullable_to_non_nullable
              as Faculty?,
      department: freezed == department
          ? _value.department
          : department // ignore: cast_nullable_to_non_nullable
              as Department?,
      programme: freezed == programme
          ? _value.programme
          : programme // ignore: cast_nullable_to_non_nullable
              as Programme?,
      academicLevel: freezed == academicLevel
          ? _value.academicLevel
          : academicLevel // ignore: cast_nullable_to_non_nullable
              as AcademicLevel?,
      admissionYear: freezed == admissionYear
          ? _value.admissionYear
          : admissionYear // ignore: cast_nullable_to_non_nullable
              as String?,
      graduationYear: freezed == graduationYear
          ? _value.graduationYear
          : graduationYear // ignore: cast_nullable_to_non_nullable
              as String?,
      programDuration: freezed == programDuration
          ? _value.programDuration
          : programDuration // ignore: cast_nullable_to_non_nullable
              as String?,
      admissionType: freezed == admissionType
          ? _value.admissionType
          : admissionType // ignore: cast_nullable_to_non_nullable
              as String?,
      role: freezed == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as String?,
      currentAcademicSessionWithSemesters: freezed ==
              currentAcademicSessionWithSemesters
          ? _value.currentAcademicSessionWithSemesters
          : currentAcademicSessionWithSemesters // ignore: cast_nullable_to_non_nullable
              as Session?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserImpl implements _User {
  const _$UserImpl(
      {required this.id,
      @JsonKey(name: 'user_id') this.userId,
      this.fullname,
      @JsonKey(name: 'profile_picture_url') this.profilePictureUrl,
      @JsonKey(name: 'student_number') this.studentNumber,
      @JsonKey(name: 'jamb_registration_number') this.jambRegistrationNumber,
      this.email,
      @JsonKey(name: 'phone_number') this.phoneNumber,
      this.gender,
      this.nationality,
      @JsonKey(name: 'state_of_origin') this.stateOfOrigin,
      this.lga,
      @JsonKey(name: 'date_of_birth') this.dateOfBirth,
      @JsonKey(name: 'blood_group') this.bloodGroup,
      this.genotype,
      this.religion,
      @JsonKey(name: 'marital_status') this.maritalStatus,
      this.faculty,
      this.department,
      this.programme,
      @JsonKey(name: 'academic_level') this.academicLevel,
      @JsonKey(name: 'admission_year') this.admissionYear,
      @JsonKey(name: 'graduation_year') this.graduationYear,
      @JsonKey(name: 'program_duration') this.programDuration,
      @JsonKey(name: 'admission_type') this.admissionType,
      this.role,
      @JsonKey(name: 'current_academic_session_with_semesters')
      this.currentAcademicSessionWithSemesters});

  factory _$UserImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserImplFromJson(json);

  @override
  final int id;
  @override
  @JsonKey(name: 'user_id')
  final int? userId;
  @override
  final String? fullname;
  @override
  @JsonKey(name: 'profile_picture_url')
  final String? profilePictureUrl;
  @override
  @JsonKey(name: 'student_number')
  final String? studentNumber;
  @override
  @JsonKey(name: 'jamb_registration_number')
  final String? jambRegistrationNumber;
  @override
  final String? email;
  @override
  @JsonKey(name: 'phone_number')
  final String? phoneNumber;
  @override
  final String? gender;
  @override
  final String? nationality;
  @override
  @JsonKey(name: 'state_of_origin')
  final String? stateOfOrigin;
  @override
  final String? lga;
  @override
  @JsonKey(name: 'date_of_birth')
  final String? dateOfBirth;
  @override
  @JsonKey(name: 'blood_group')
  final String? bloodGroup;
  @override
  final String? genotype;
  @override
  final String? religion;
  @override
  @JsonKey(name: 'marital_status')
  final String? maritalStatus;
  @override
  final Faculty? faculty;
  @override
  final Department? department;
  @override
  final Programme? programme;
  @override
  @JsonKey(name: 'academic_level')
  final AcademicLevel? academicLevel;
  @override
  @JsonKey(name: 'admission_year')
  final String? admissionYear;
  @override
  @JsonKey(name: 'graduation_year')
  final String? graduationYear;
  @override
  @JsonKey(name: 'program_duration')
  final String? programDuration;
  @override
  @JsonKey(name: 'admission_type')
  final String? admissionType;
  @override
  final String? role;
  @override
  @JsonKey(name: 'current_academic_session_with_semesters')
  final Session? currentAcademicSessionWithSemesters;

  @override
  String toString() {
    return 'User(id: $id, userId: $userId, fullname: $fullname, profilePictureUrl: $profilePictureUrl, studentNumber: $studentNumber, jambRegistrationNumber: $jambRegistrationNumber, email: $email, phoneNumber: $phoneNumber, gender: $gender, nationality: $nationality, stateOfOrigin: $stateOfOrigin, lga: $lga, dateOfBirth: $dateOfBirth, bloodGroup: $bloodGroup, genotype: $genotype, religion: $religion, maritalStatus: $maritalStatus, faculty: $faculty, department: $department, programme: $programme, academicLevel: $academicLevel, admissionYear: $admissionYear, graduationYear: $graduationYear, programDuration: $programDuration, admissionType: $admissionType, role: $role, currentAcademicSessionWithSemesters: $currentAcademicSessionWithSemesters)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.fullname, fullname) ||
                other.fullname == fullname) &&
            (identical(other.profilePictureUrl, profilePictureUrl) ||
                other.profilePictureUrl == profilePictureUrl) &&
            (identical(other.studentNumber, studentNumber) ||
                other.studentNumber == studentNumber) &&
            (identical(other.jambRegistrationNumber, jambRegistrationNumber) ||
                other.jambRegistrationNumber == jambRegistrationNumber) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.gender, gender) || other.gender == gender) &&
            (identical(other.nationality, nationality) ||
                other.nationality == nationality) &&
            (identical(other.stateOfOrigin, stateOfOrigin) ||
                other.stateOfOrigin == stateOfOrigin) &&
            (identical(other.lga, lga) || other.lga == lga) &&
            (identical(other.dateOfBirth, dateOfBirth) ||
                other.dateOfBirth == dateOfBirth) &&
            (identical(other.bloodGroup, bloodGroup) ||
                other.bloodGroup == bloodGroup) &&
            (identical(other.genotype, genotype) ||
                other.genotype == genotype) &&
            (identical(other.religion, religion) ||
                other.religion == religion) &&
            (identical(other.maritalStatus, maritalStatus) ||
                other.maritalStatus == maritalStatus) &&
            (identical(other.faculty, faculty) || other.faculty == faculty) &&
            (identical(other.department, department) ||
                other.department == department) &&
            (identical(other.programme, programme) ||
                other.programme == programme) &&
            (identical(other.academicLevel, academicLevel) ||
                other.academicLevel == academicLevel) &&
            (identical(other.admissionYear, admissionYear) ||
                other.admissionYear == admissionYear) &&
            (identical(other.graduationYear, graduationYear) ||
                other.graduationYear == graduationYear) &&
            (identical(other.programDuration, programDuration) ||
                other.programDuration == programDuration) &&
            (identical(other.admissionType, admissionType) ||
                other.admissionType == admissionType) &&
            (identical(other.role, role) || other.role == role) &&
            (identical(other.currentAcademicSessionWithSemesters,
                    currentAcademicSessionWithSemesters) ||
                other.currentAcademicSessionWithSemesters ==
                    currentAcademicSessionWithSemesters));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        userId,
        fullname,
        profilePictureUrl,
        studentNumber,
        jambRegistrationNumber,
        email,
        phoneNumber,
        gender,
        nationality,
        stateOfOrigin,
        lga,
        dateOfBirth,
        bloodGroup,
        genotype,
        religion,
        maritalStatus,
        faculty,
        department,
        programme,
        academicLevel,
        admissionYear,
        graduationYear,
        programDuration,
        admissionType,
        role,
        currentAcademicSessionWithSemesters
      ]);

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserImplCopyWith<_$UserImpl> get copyWith =>
      __$$UserImplCopyWithImpl<_$UserImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserImplToJson(
      this,
    );
  }
}

abstract class _User implements User {
  const factory _User(
      {required final int id,
      @JsonKey(name: 'user_id') final int? userId,
      final String? fullname,
      @JsonKey(name: 'profile_picture_url') final String? profilePictureUrl,
      @JsonKey(name: 'student_number') final String? studentNumber,
      @JsonKey(name: 'jamb_registration_number')
      final String? jambRegistrationNumber,
      final String? email,
      @JsonKey(name: 'phone_number') final String? phoneNumber,
      final String? gender,
      final String? nationality,
      @JsonKey(name: 'state_of_origin') final String? stateOfOrigin,
      final String? lga,
      @JsonKey(name: 'date_of_birth') final String? dateOfBirth,
      @JsonKey(name: 'blood_group') final String? bloodGroup,
      final String? genotype,
      final String? religion,
      @JsonKey(name: 'marital_status') final String? maritalStatus,
      final Faculty? faculty,
      final Department? department,
      final Programme? programme,
      @JsonKey(name: 'academic_level') final AcademicLevel? academicLevel,
      @JsonKey(name: 'admission_year') final String? admissionYear,
      @JsonKey(name: 'graduation_year') final String? graduationYear,
      @JsonKey(name: 'program_duration') final String? programDuration,
      @JsonKey(name: 'admission_type') final String? admissionType,
      final String? role,
      @JsonKey(name: 'current_academic_session_with_semesters')
      final Session? currentAcademicSessionWithSemesters}) = _$UserImpl;

  factory _User.fromJson(Map<String, dynamic> json) = _$UserImpl.fromJson;

  @override
  int get id;
  @override
  @JsonKey(name: 'user_id')
  int? get userId;
  @override
  String? get fullname;
  @override
  @JsonKey(name: 'profile_picture_url')
  String? get profilePictureUrl;
  @override
  @JsonKey(name: 'student_number')
  String? get studentNumber;
  @override
  @JsonKey(name: 'jamb_registration_number')
  String? get jambRegistrationNumber;
  @override
  String? get email;
  @override
  @JsonKey(name: 'phone_number')
  String? get phoneNumber;
  @override
  String? get gender;
  @override
  String? get nationality;
  @override
  @JsonKey(name: 'state_of_origin')
  String? get stateOfOrigin;
  @override
  String? get lga;
  @override
  @JsonKey(name: 'date_of_birth')
  String? get dateOfBirth;
  @override
  @JsonKey(name: 'blood_group')
  String? get bloodGroup;
  @override
  String? get genotype;
  @override
  String? get religion;
  @override
  @JsonKey(name: 'marital_status')
  String? get maritalStatus;
  @override
  Faculty? get faculty;
  @override
  Department? get department;
  @override
  Programme? get programme;
  @override
  @JsonKey(name: 'academic_level')
  AcademicLevel? get academicLevel;
  @override
  @JsonKey(name: 'admission_year')
  String? get admissionYear;
  @override
  @JsonKey(name: 'graduation_year')
  String? get graduationYear;
  @override
  @JsonKey(name: 'program_duration')
  String? get programDuration;
  @override
  @JsonKey(name: 'admission_type')
  String? get admissionType;
  @override
  String? get role;
  @override
  @JsonKey(name: 'current_academic_session_with_semesters')
  Session? get currentAcademicSessionWithSemesters;

  /// Create a copy of User
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserImplCopyWith<_$UserImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

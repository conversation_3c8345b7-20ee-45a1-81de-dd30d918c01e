import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:tertiary_mobile/core/constants/assets.dart';
import 'package:tertiary_mobile/core/constants/colors.dart';
import 'package:tertiary_mobile/routing/router.dart';
import 'package:tertiary_mobile/shared/presentation/layout/main_app_custom_appbar.dart';
import 'package:tertiary_mobile/shared/presentation/widgets/largeTextfield.dart';
import 'package:tertiary_mobile/shared/presentation/widgets/supportdropdown.dart';
import 'package:tertiary_mobile/shared/presentation/widgets/text_field.dart';

class SupportScreen extends ConsumerStatefulWidget {
  const SupportScreen({super.key});

  @override
  ConsumerState<SupportScreen> createState() => _SupportScreenState();
}

class _SupportScreenState extends ConsumerState<SupportScreen> {
  final TextEditingController _subjectController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();

  String? _selectedSupport;

  @override
  void dispose() {
    _subjectController.dispose();
    _descriptionController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colors = ref.watch(appColorsProvider);
    final GoRouter router = ref.read(routerProvider);

    return Scaffold(
      appBar: CustomAppBar(title: 'Support'),
      backgroundColor: Colors.white,
      body: Padding(
        padding: EdgeInsets.all(16.w),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Need Help? Raise a Ticket',
                style: TextStyle(
                  fontSize: 20.sp,
                  fontWeight: FontWeight.w500,
                  color: colors.primary,
                ),
              ),
              SizedBox(height: 10.h),
              Text(
                'Describe your issue and our team will get back to you as soon as possible.',
                style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w400),
              ),
              SizedBox(height: 20.h),
              Text(
                'Select Category',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                  color: colors.primary,
                ),
              ),
              SizedBox(height: 10.h),
              SupportDropdown(
                label: 'Support Category',
                selectedSupport: _selectedSupport,
                onChanged: (value) {
                  setState(() {
                    _selectedSupport = value;
                  });
                },
              ),
              SizedBox(height: 10.h),
              Text(
                'Subject',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                  color: colors.primary,
                ),
              ),
              SizedBox(height: 10.h),
              StryAppTextField(
                label: 'Enter Subject',
                controller: _subjectController,
              ),
              SizedBox(height: 10.h),
              Text(
                'Description',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                  color: colors.primary,
                ),
              ),
              SizedBox(height: 10.h),
              LargeTextField(
                hintText: 'Describe your issue here',
                controller: _descriptionController,
              ),
              SizedBox(height: 10.h),
              Text(
                'Email',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                  color: colors.primary,
                ),
              ),
              SizedBox(height: 10.h),
              StryAppTextField(
                label: 'Enter your email',
                controller: _emailController,
              ),
              SizedBox(height: 10.h),
              Text(
                'Phone Number',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                  color: colors.primary,
                ),
              ),
              SizedBox(height: 10.h),
              StryAppTextField(
                label: 'Enter your phone number',
                controller: _phoneController,
              ),
              SizedBox(height: 20.h),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => _showConfirmDialog(context, colors, router),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: colors.primary,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    padding: EdgeInsets.symmetric(
                      horizontal: 24.w,
                      vertical: 12.h,
                    ),
                  ),
                  child: Text(
                    'Submit',
                    style: TextStyle(color: Colors.white, fontSize: 16.sp),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showConfirmDialog(
    BuildContext context,
    AppColors colors,
    GoRouter router,
  ) {
    showDialog(
      context: context,
      builder: (_) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        insetPadding: EdgeInsets.symmetric(horizontal: 40.w),
        child: Padding(
          padding: EdgeInsets.all(24.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(Assets.correct, scale: 2),
              SizedBox(height: 16.h),
              Text(
                'Your ticket has been submitted. Our support team will reach out via email within 24 hours.',
                textAlign: TextAlign.center,
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

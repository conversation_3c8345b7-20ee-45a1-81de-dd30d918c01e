import 'dart:io';

import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:tertiary_mobile/core/utils/logger.dart';
import 'package:tertiary_mobile/features/announcement/data/models/announcements_table.dart';
import 'package:tertiary_mobile/shared/data/models/sessions_table.dart';
import 'package:tertiary_mobile/shared/data/models/semesters_table.dart';
import 'package:tertiary_mobile/features/courses/data/tables/courses_table.dart';
import 'package:tertiary_mobile/features/courses/data/tables/course_registrations_table.dart';
import 'package:tertiary_mobile/features/courses/data/tables/course_specifications_table.dart';
import 'package:tertiary_mobile/core/cache/cache_metadata_table.dart';

part 'local_database.g.dart';

@DriftDatabase(
  tables: [
    AnnouncementsTable,
    SessionsTable,
    SemestersTable,
    CoursesTable,
    CourseRegistrationsTable,
    CourseSpecificationsTable,
    CacheMetadataTable,
  ],
)
class LocalDatabase extends _$LocalDatabase {
  LocalDatabase([QueryExecutor? executor])
      : super(executor ?? _openConnection());

  @override
  int get schemaVersion => 1;

  @override
  MigrationStrategy get migration => MigrationStrategy(
        onCreate: (m) async {
          await m.createAll();
          logger.i('Database created with schema version 1');
        },
        beforeOpen: (details) async {
          if (details.wasCreated) {
            logger.i('Database created with version ${details.versionNow}');
          }

          // Enable foreign keys
          await customStatement('PRAGMA foreign_keys = ON');
        },
      );
}

LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, 'db.sqlite'));

    return NativeDatabase.createInBackground(file);
  });
}

@Riverpod(keepAlive: true)
LocalDatabase localDatabase(Ref ref) {
  return LocalDatabase();
}

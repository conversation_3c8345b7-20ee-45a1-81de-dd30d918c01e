import 'package:flutter/material.dart';
import 'dropdown.dart';

class SupportDropdown extends StatelessWidget {
  final String? label;
  final String? selectedSupport;
  final void Function(String?) onChanged;

  const SupportDropdown({
    super.key,
    this.label,
    required this.selectedSupport,
    required this.onChanged,
  });

  static const List<String> _options = [
    'Academic',
    'Technical',
    'Finance',
    'Other',
  ];

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 70,
      child: StryDropdown(
        label: label ?? 'Support Type',
        items: _options,
        selectedValue: selectedSupport,
        onChanged: onChanged,
        showPrefixIcon: false,
      ),
    );
  }
}
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:tertiary_mobile/core/constants/assets.dart';
import 'package:tertiary_mobile/core/constants/colors.dart';

class StryDropdown extends HookConsumerWidget {
  final String label;
  final List<String> items;
  final ValueChanged<String?> onChanged;
  final String? selectedValue;
  final bool enabled;

  /// New flag: show or hide the prefix icon
  final bool showPrefixIcon;

  const StryDropdown({
    super.key,
    required this.label,
    required this.items,
    required this.onChanged,
    this.selectedValue,
    this.enabled = true,
    this.showPrefixIcon = true,  
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colors = ref.watch(appColorsProvider);
    final borderColor = Colors.grey.shade300;
    final labelStyle = TextStyle(
      color: colors.primary.withValues(alpha: 0.5),
      fontSize: 12,
    );
    final itemStyle = TextStyle(
      color: colors.primary,
      fontSize: 14,
      fontWeight: FontWeight.bold,
      overflow: TextOverflow.ellipsis,
    );

    final value = useState<String?>(selectedValue);

    // Keep value in sync with selectedValue prop
    useEffect(() {
      // Ensure the selected value exists in the items list
      if (selectedValue != null && items.contains(selectedValue)) {
        value.value = selectedValue;
      } else if (items.isNotEmpty) {
        // If selected value is not in items, reset to first item
        value.value = items.first;
      } else {
        value.value = null;
      }
      return null;
    }, [selectedValue, items]);

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: borderColor),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String?>(
          value: value.value,
          hint: Row(
            children: [
              if (showPrefixIcon) ...[
                Image.asset(Assets.dropdownicon, scale: 2),
                const SizedBox(width: 6),
              ],
              Text(label, style: labelStyle),
            ],
          ),
          icon: Icon(
            Icons.keyboard_arrow_down,
            color: colors.primary.withValues(alpha: 0.5),
          ),
          items: items
              .map((item) => DropdownMenuItem<String?>(
                    value: item,
                    child: Text(item, style: itemStyle),
                  ))
              .toList(),
          onChanged: enabled
              ? (String? newValue) {
                  value.value = newValue;
                  onChanged(newValue);
                }
              : null,
        ),
      ),
    );
  }
}

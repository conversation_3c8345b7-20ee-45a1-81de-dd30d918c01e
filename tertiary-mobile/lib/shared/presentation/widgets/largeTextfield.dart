import 'package:flutter/material.dart';

class LargeTextField extends StatefulWidget {
  final String? label;
  final String? hintText;
  final int maxLength;
  final TextEditingController? controller;
  final ValueChanged<String>? onChanged;
  final bool enabled;
  final String? initialValue;
  final bool isRequired;

  const LargeTextField({
    super.key,
    this.label,
    this.hintText,
    this.maxLength = 5000,
    this.controller,
    this.onChanged,
    this.enabled = true,
    this.initialValue,
    this.isRequired = false,
  });

  @override
  State<LargeTextField> createState() => _LargeTextFieldState();
}

class _LargeTextFieldState extends State<LargeTextField> {
  late TextEditingController _internalController;
  late FocusNode _focusNode;
  int _currentLength = 0;

  @override
  void initState() {
    super.initState();
    _internalController =
        widget.controller ?? TextEditingController(text: widget.initialValue);
    _focusNode = FocusNode();
    _internalController.addListener(_onTextChanged);
    _focusNode.addListener(() {
      setState(() {}); 
    });
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _internalController.dispose();
    }
    _focusNode.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final text = _internalController.text;
    if (text.length > widget.maxLength) {
      _internalController.text = text.substring(0, widget.maxLength);
      // Move cursor to the end.
      _internalController.selection = TextSelection.fromPosition(
        TextPosition(offset: _internalController.text.length),
      );
    }

    setState(() {
      _currentLength = _internalController.text.length;
    });

    widget.onChanged?.call(_internalController.text);
  }
  Color _getBorderColor() {
    if (_focusNode.hasFocus) {
      return Colors.green;
    }
    return Colors.grey.shade300;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          constraints: const BoxConstraints(minHeight: 150),
          decoration: BoxDecoration(
            color:  Colors.transparent,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: _getBorderColor()),
          ),
          child: TextFormField(
            cursorColor: Colors.black,
            controller: _internalController,
            focusNode: _focusNode,
            enabled: widget.enabled,
            maxLines: null, 
            keyboardType: TextInputType.multiline,
            decoration: InputDecoration(
              hintText: widget.hintText,
              hintStyle: TextStyle(color: Colors.grey.shade400),
              contentPadding: const EdgeInsets.all(12),
              border: InputBorder.none, 
            ),
            style: const TextStyle(fontSize: 14),
          ),
        ),
        const SizedBox(height: 4),

        // Footer row: "Maximum of X characters" on the left, "Count: Y" on the right
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Maximum of ${widget.maxLength} characters',
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
            Text(
              'Count: $_currentLength',
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ],
    );
  }
}

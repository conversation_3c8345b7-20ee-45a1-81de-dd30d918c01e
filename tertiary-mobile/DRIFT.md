# 🗄️ Drift Database Guide

## 📋 **Overview**

This guide explains the Drift database setup in the SchoolTry Tertiary Mobile app. During development, we use a simple first-migration approach for flexibility. This document also covers how to transition to production-ready migrations when needed.

## 🏗️ **Current Setup (Development Mode)**

### **Database Configuration**
- **Database File**: `lib/core/utils/local_database.dart`
- **Current Schema Version**: `1` (Development - First Migration)
- **Migration System**: Simple `onCreate` approach (no incremental migrations)
- **Approach**: Clean slate on schema changes during development

### **Build Configuration**
```yaml
# build.yaml
targets:
  $default:
    builders:
      drift_dev:
        options:
          databases:
            local_database: lib/core/utils/local_database.dart
```

### **Database Structure**
- **Tables**: Announcements, Sessions, Semesters, Courses, Course Registrations, Course Specifications, Cache Metadata
- **Relationships**: Foreign key constraints between related tables
- **Indexes**: Optimized for common query patterns

### **Development Benefits**
- ✅ **Fast Development**: No migration complexity during development
- ✅ **Easy Schema Changes**: Simply update tables and rebuild
- ✅ **Clean Testing**: Fresh database state for each schema change
- ✅ **Simplified Debugging**: No migration logic to troubleshoot

## 🚀 **Development Workflow: Adding/Updating Tables**

### **Step 1: Make Schema Changes**

#### **Adding a New Table**
```dart
// lib/features/payments/data/tables/payments_table.dart
@DataClassName('Payment')
class PaymentsTable extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get studentId => text()();
  TextColumn get amount => text()();
  TextColumn get status => text()();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
}
```

#### **Adding Columns to Existing Table**
```dart
// lib/features/announcement/data/models/announcements_table.dart
@DataClassName('Announcement')
class AnnouncementsTable extends Table {
  // ... existing columns ...
  
  // NEW COLUMNS
  BoolColumn get isRead => boolean().withDefault(const Constant(false))();
  DateTimeColumn get readAt => dateTime().nullable()();
}
```

#### **Update Database Class**
```dart
// lib/core/utils/local_database.dart
@DriftDatabase(tables: [
  AnnouncementsTable,
  SessionsTable,
  SemestersTable,
  CoursesTable,
  CourseRegistrationsTable,
  CourseSpecificationsTable,
  PaymentsTable, // ADD NEW TABLE HERE
])
class LocalDatabase extends _$LocalDatabase {
  // ...
}
```

### **Step 2: Rebuild Generated Code**
```bash
# Rebuild Drift generated code
dart run build_runner build --delete-conflicting-outputs
```

### **Step 3: Test Your Changes**
```bash
# Run your app and test the new schema
flutter run

# Or run tests if you have database tests
flutter test
```

**That's it!** During development, schema changes are applied immediately when the app starts with a fresh database.

## ⚠️ **Development Considerations**

### **Data Loss Warning**
- 🚨 **Schema changes will cause data loss** during development
- 🚨 **Users will need to reinstall the app** or clear app data
- 🚨 **This is expected and acceptable during development**

### **When Schema Changes Occur**
- Adding/removing tables
- Adding/removing columns
- Changing column types or constraints
- Modifying indexes or foreign keys

### **Testing Strategy**
1. **Test with fresh installs** - Ensure new users can use the app
2. **Test schema changes** - Verify tables are created correctly
3. **Test data operations** - Ensure CRUD operations work
4. **Test relationships** - Verify foreign keys and joins work

## 🔄 **Transitioning to Production Migrations**

When you're ready to ship to production, you'll need to implement proper database migrations to handle schema changes safely for existing users.

### **When to Transition**
- ✅ **Before first production release**
- ✅ **When schema is relatively stable**
- ✅ **Before distributing to external testers**
- ✅ **When you need to preserve user data**

### **Transition Steps**

#### **Step 1: Finalize Your Schema**
Ensure your current schema represents your "production v1":
```dart
// lib/core/utils/local_database.dart
@override
int get schemaVersion => 1; // This becomes your production baseline
```

#### **Step 2: Update Build Configuration**
```yaml
# build.yaml
targets:
  $default:
    builders:
      drift_dev:
        options:
          databases:
            local_database: lib/core/utils/local_database.dart
          test_dir: test/migrations/        # Add back for production
          schema_dir: drift_schemas/        # Add back for production
```

#### **Step 3: Generate Migration Infrastructure**
```bash
# Generate migration files
dart run drift_dev make-migrations
```

This creates:
- `drift_schemas/drift_schema_v1.json` - Your baseline schema
- `test/migrations/local_database/` - Migration test infrastructure

#### **Step 4: Import Step-by-Step Migration Helper**
```dart
// lib/core/utils/local_database.dart
import 'local_database.steps.dart';  // Add this import back
```

#### **Step 5: Implement Production Migration Strategy**
```dart
// lib/core/utils/local_database.dart
@override
MigrationStrategy get migration => MigrationStrategy(
  onCreate: (m) async {
    await m.createAll();
    logger.i('Database created with schema version ${schemaVersion}');
  },
  onUpgrade: stepByStep(
    // Future migrations will be added here as needed
    // from1To2: (m, schema) async { ... },
    // from2To3: (m, schema) async { ... },
  ),
  beforeOpen: (details) async {
    if (details.wasCreated) {
      logger.i('Database created with version ${details.versionNow}');
    } else if (details.hadUpgrade) {
      logger.i('Database migrated from ${details.versionBefore} to ${details.versionNow}');
    }
    
    // Enable foreign keys
    await customStatement('PRAGMA foreign_keys = ON');
  },
);
```

### **Future Schema Changes (Production)**

Once in production mode, follow this process for schema changes:

#### **1. Make Schema Changes**
- Add new tables or columns to your Dart table definitions
- Update the database class to include new tables

#### **2. Increment Schema Version**
```dart
@override
int get schemaVersion => 2; // Increment from 1 to 2
```

#### **3. Generate Migration Files**
```bash
dart run drift_dev make-migrations
```

#### **4. Implement Migration Logic**
```dart
onUpgrade: stepByStep(
  from1To2: (m, schema) async {
    logger.i('Migrating from version 1 to 2');
    
    // Add new table
    await m.createTable(schema.newTable);
    
    // Add new columns with defaults
    await m.addColumn(schema.existingTable, schema.existingTable.newColumn);
    
    logger.i('Successfully migrated to version 2');
  },
),
```

#### **5. Test Migrations**
```bash
flutter test test/migrations/local_database/migration_test.dart
```

## ⚠️ **Production Safety Guidelines**

### **DO's ✅**
1. **Always increment schema version** when making changes
2. **Run `make-migrations`** after schema changes
3. **Test migrations thoroughly** before deployment
4. **Use default values** for new non-nullable columns
5. **Make new columns nullable** when possible
6. **Add indexes in migrations** for performance
7. **Use transactions** for complex migrations

### **DON'Ts ❌**
1. **Never skip schema versions** (e.g., don't go from v1 to v3)
2. **Never modify existing migrations** once deployed
3. **Don't remove columns** without careful planning
4. **Don't change column types** without data conversion
5. **Don't deploy without testing migrations**

### **Safe Migration Patterns**

#### **Adding Columns (Safe)**
```dart
// ✅ SAFE: Add nullable column
await m.addColumn(schema.usersTable, schema.usersTable.phoneNumber);

// ✅ SAFE: Add column with default value
await m.addColumn(schema.usersTable, schema.usersTable.isActive);
```

#### **Removing Columns (Risky)**
```dart
// ❌ RISKY: Don't do this directly
// await m.dropColumn(schema.usersTable, 'oldColumn');

// ✅ SAFER: Mark as deprecated first, remove in later version
// 1. Deploy version that ignores the column
// 2. Later deploy version that removes it
```

#### **Renaming Columns (Complex)**
```dart
// ✅ SAFE: Create new column, copy data, remove old
await m.addColumn(schema.usersTable, schema.usersTable.fullName);
await m.customStatement(
  'UPDATE users_table SET full_name = first_name || " " || last_name'
);
// Remove old columns in next migration
```

## 🧪 **Testing Strategy**

### **Development Testing**
```bash
# Test fresh app installs
flutter run

# Test with different data scenarios
flutter test
```

### **Production Migration Testing**
```bash
# Test all migrations
flutter test test/migrations/

# Test specific migration
flutter test test/migrations/local_database/migration_test.dart
```

## 🔗 **Useful Commands**

### **Development Commands**
```bash
# Rebuild generated code after schema changes
dart run build_runner build --delete-conflicting-outputs

# Clean build (if issues occur)
dart run build_runner clean
flutter clean && flutter pub get
```

### **Production Migration Commands**
```bash
# Generate migrations after schema changes
dart run drift_dev make-migrations

# Run migration tests
flutter test test/migrations/

# Validate database schema
dart run drift_dev validate-database
```

## 📚 **Additional Resources**

- [Official Drift Documentation](https://drift.simonbinder.eu/)
- [Drift Migration Guide](https://drift.simonbinder.eu/migrations/)
- [Drift Testing Guide](https://drift.simonbinder.eu/migrations/tests/)

---

**Remember**: The development approach prioritizes speed and flexibility. Transition to production migrations when you need to preserve user data! 🛡️

<?php

namespace App\Http\Resources\MobileApp\Auth;

use App\Http\Resources\CourseResource;
use Illuminate\Http\Resources\Json\JsonResource;

class StudentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user->id,
            'profile_picture_url' => $this->picture_url ?? '',
            'fullname' => $this->firstname . ' ' . $this->lastname,
            'student_number' => $this->student_number,
            'jamb_registration_number' => $this->jamb_registration_number,
            'email' => $this->email,
            'phone_number' => $this->phone_number,
            'gender' => $this->transformEnum($this->gender, 'enums.genders'),
            'nationality' => $this->transformEnum($this->nationality, 'enums.nationality'),
            'state_of_origin' => $this->state_of_origin,
            'lga' => $this->local_government_area ?? "N/A",
            'date_of_birth' => $this->date_of_birth ?? "N/A",
            'blood_group' => $this->blood_group ? $this->transformEnum($this->blood_group, 'enums.blood_groups') : "N/A",
            'genotype' => $this->genotype ? $this->transformEnum($this->genotype, 'enums.genotypes') : "N/A",
            'religion' => $this->religion ? $this->transformEnum($this->religion, 'enums.religions') : "N/A",
            'marital_status' => $this->marital_status ? $this->transformEnum($this->marital_status, 'enums.marital_status') : "N/A",
            'faculty' => [
                'id' => optional($this->department->faculty)->id ?? "N/A",
                'name' => optional($this->department->faculty)->name ?? "N/A",
            ],
            'department' => [
                'id' => optional($this->department)->id ?? "N/A",
                'name' => optional($this->department)->name     ?? "N/A",
            ],
            'programme' => [
                'id' => optional($this->programme)->id ?? "N/A",
                'name' => optional($this->programme)->name ?? "N/A",
            ],
            'academic_level' => [
                'id' => optional($this->studentAcademicLevel)->id ?? "N/A",
                'name' => optional($this->studentAcademicLevel)->name ?? "N/A",
            ],
            'admission_year' => $this->admissionSession->session_name ?? "N/A",
            'graduation_year' => $this->graduation_year ?? "N/A",
            'program_duration' => optional($this->programme)->duration ?? "N/A",
            'admission_type' => optional($this->modeOfEntry)->name ?? "N/A",
        ];
    }

    /**
     * Transform enums using configuration.
     *
     * @param mixed $value
     * @param string $configKey
     * @return string|null
     */
    protected function transformEnum($value, $configKey)
    {
        return $value && is_numeric($value) ? array_search($value, config($configKey)) : $value;
    }
}
